import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Test widget to verify the recipe name banner functionality
class TestRecipeBanner extends StatelessWidget {
  const TestRecipeBanner({super.key});

  Widget _buildRecipeNameBanner(String recipeName) {
    // Create a beautiful banner with recipe name when image is not available
    final colors = [
      const Color(0xFF6C5CE7),
      const Color(0xFF74B9FF),
      const Color(0xFF00B894),
      const Color(0xFFFDCB6E),
      const Color(0xFFE17055),
      const Color(0xFFFF6B6B),
      const Color(0xFFA29BFE),
      const Color(0xFF81ECEC),
    ];
    
    // Use recipe name hash to consistently pick a color
    final colorIndex = recipeName.hashCode.abs() % colors.length;
    final backgroundColor = colors[colorIndex];
    
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            backgroundColor,
            backgroundColor.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.restaurant_menu,
                size: 24,
                color: Colors.white,
              ),
              const SizedBox(height: 4),
              Text(
                recipeName,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.plusJakartaSans(
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  height: 1.2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Recipe Banner Test')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            Container(
              height: 120,
              child: _buildRecipeNameBanner('Kerala Fish Curry'),
            ),
            Container(
              height: 120,
              child: _buildRecipeNameBanner('Chicken Biriyani'),
            ),
            Container(
              height: 120,
              child: _buildRecipeNameBanner('Masala Dosa'),
            ),
            Container(
              height: 120,
              child: _buildRecipeNameBanner('Butter Chicken'),
            ),
            Container(
              height: 120,
              child: _buildRecipeNameBanner('Appam with Stew'),
            ),
            Container(
              height: 120,
              child: _buildRecipeNameBanner('Puttu and Kadala Curry'),
            ),
          ],
        ),
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(
    home: TestRecipeBanner(),
  ));
}
