{"app": {"title": "Kerala Family App", "subtitle": "Connecting families, preserving heritage"}, "welcome": {"title": "Welcome to Kerala Family", "subtitle": "Connect with your family, preserve your heritage, and celebrate traditions together", "getStarted": "Get Started", "description": "Join thousands of Kerala families in preserving their rich cultural heritage and staying connected across generations."}, "auth": {"createAccount": "Create Account", "login": "<PERSON><PERSON>", "email": "Email", "phone": "Phone Number", "password": "Password", "confirmPassword": "Confirm Password", "fullName": "Full Name", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signUp": "Sign Up", "signIn": "Sign In", "forgotPassword": "Forgot Password?", "or": "or", "enterEmail": "Enter your email", "enterPhone": "Enter your phone number", "enterPassword": "Enter your password", "enterFullName": "Enter your full name", "confirmPasswordHint": "Re-enter your password", "invalidEmail": "Please enter a valid email", "invalidPhone": "Please enter a valid phone number", "passwordTooShort": "Password must be at least 6 characters", "passwordsDoNotMatch": "Passwords do not match", "nameRequired": "Full name is required", "loginSuccess": "Login successful!", "accountCreated": "Account created successfully!", "validation": {"emailOrPhoneRequired": "Please enter your email or phone number", "validEmailRequired": "Please enter a valid email address", "validPhoneRequired": "Please enter a valid phone number", "passwordRequired": "Please enter your password"}, "messages": {"welcomeBack": "Welcome back!", "accountCreatedSuccess": "Account created successfully! Please check your email for verification.", "accountCreatedPhoneSuccess": "Account created successfully! Please verify your phone number.", "signInFailed": "Failed to sign in", "createAccountFailed": "Failed to create account", "verificationCodeSent": "Verification code sent successfully", "verificationCodeFailed": "Failed to send verification code", "emailVerifiedSuccess": "Email verified successfully!", "phoneVerifiedSuccess": "Phone number verified successfully!", "verificationFailed": "Failed to verify code", "invalidVerificationCode": "Invalid verification code", "passwordResetEmailSent": "Password reset email sent successfully", "passwordResetPhoneNotSupported": "Password reset via phone number is not supported yet", "passwordResetFailed": "Failed to reset password", "profileUpdatedSuccess": "Profile updated successfully", "profileUpdateFailed": "Failed to update profile", "accountDeletionInitiated": "Account deletion initiated. Please contact support to complete the process.", "accountDeletionFailed": "Failed to delete account", "invalidCredentials": "Invalid credentials", "failedToCreateAccount": "Failed to create account", "failedToSignIn": "Failed to sign in", "failedToSendVerificationCode": "Failed to send verification code", "failedToVerifyCode": "Failed to verify code", "failedToResetPassword": "Failed to reset password", "failedToUpdateProfile": "Failed to update profile", "failedToDeleteAccount": "Failed to delete account", "googleSignInSuccess": "Successfully signed in with Google", "googleSignInFailed": "Google sign-in failed", "googleSignInCancelled": "Google sign-in was cancelled", "googleSignInError": "Google sign-in error", "appleSignInSuccess": "Successfully signed in with Apple", "appleSignInFailed": "Apple sign-in failed", "appleSignInError": "Apple sign-in error"}, "buttons": {"continue": "Continue", "sendCode": "Send Code", "verifyCode": "Verify Code", "resendCode": "Resend Code", "resetPassword": "Reset Password", "updateProfile": "Update Profile", "deleteAccount": "Delete Account", "signInWithGoogle": "Continue with Google", "signInWithApple": "Continue with Apple"}, "placeholders": {"emailOrPhone": "Email or Phone Number", "phoneNumberOrEmail": "Phone number or email", "password": "Password", "verificationCode": "Enter verification code"}, "titles": {"verification": "Verification", "resetPassword": "Reset Password", "updateProfile": "Update Profile"}, "terms": {"termsAndConditions": "By continuing, you agree to our Terms of Service and Privacy Policy.", "alreadyHaveAccountQuestion": "Already have an account? "}}, "dashboard": {"welcomeBack": "Welcome back,", "quickActions": "Quick Actions", "recentActivities": "Recent Activities", "addFamilyMember": "Add Family Member", "createEvent": "Create Event", "uploadPhoto": "Upload Photo", "familyTree": "Family Tree"}, "navigation": {"home": "Home", "family": "Family", "events": "Events", "memories": "Memories", "profile": "Profile"}, "profile": {"title": "Profile", "editProfile": "Edit Profile", "myEvents": "My Events", "myPhotos": "My Photos", "privacySettings": "Privacy Settings", "helpSupport": "Help & Support", "logout": "Logout", "familyMembers": "Family Members", "stats": {"familyMembers": "Family Members", "events": "Events", "photos": "Photos"}}, "family": {"title": "Family Tree", "addMember": "Add Family Member", "search": "Search Family", "familyMembers": "Family Members", "relations": {"father": "Father", "mother": "Mother", "son": "Son", "daughter": "Daughter", "grandfather": "Grandfather", "grandmother": "Grandmother", "uncle": "Uncle", "aunt": "Aunt", "cousin": "Cousin", "brother": "Brother", "sister": "Sister"}}, "events": {"title": "Events", "addEvent": "Add Event", "monthly": "Monthly", "weekly": "Weekly", "upcomingEvents": "Upcoming Events", "pastEvents": "Past Events", "familyGathering": "Family Get-Together", "diwaliCelebrations": "Diwali Celebrations", "onamFestival": "Onam Festival", "independenceDay": "Independence Day"}, "memories": {"title": "Memories", "uploadPhoto": "Upload Photo", "categories": {"all": "All", "family": "Family", "events": "Events", "festivals": "Festivals", "birthdays": "Birthdays"}, "sections": {"recentPhotos": "Recent Photos", "onamFestival": "Onam Festival 2024", "familyGatherings": "Family Gatherings"}, "photoCount": "{count} photos"}, "settings": {"title": "Settings", "language": "Language", "theme": "Theme", "notifications": "Notifications", "account": "Account", "pushNotifications": "Push Notifications", "manageAccount": "Manage Account", "themes": {"light": "Light", "dark": "Dark"}, "languages": {"english": "English", "malayalam": "Malayalam"}}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "sort": "Sort", "loading": "Loading...", "error": "Error", "success": "Success", "retry": "Retry", "close": "Close", "ok": "OK", "yes": "Yes", "no": "No", "comingSoon": "Coming Soon!", "refresh": "Refresh", "networkError": "Network connection error", "serverError": "Server error occurred", "unknownError": "An unknown error occurred", "tryAgain": "Please try again", "sessionExpired": "Your session has expired. Please sign in again."}, "dialogs": {"logout": {"title": "Logout", "message": "Are you sure you want to logout?", "confirm": "Logout", "cancel": "Cancel"}}, "time": {"justNow": "Just now", "minuteAgo": "{count} minute ago", "minutesAgo": "{count} minutes ago", "hourAgo": "{count} hour ago", "hoursAgo": "{count} hours ago", "dayAgo": "{count} day ago", "daysAgo": "{count} days ago", "weekAgo": "{count} week ago", "weeksAgo": "{count} weeks ago"}}