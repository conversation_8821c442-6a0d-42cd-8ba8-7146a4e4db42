/// Google Sign-In Configuration for Supabase
///
/// Replace these values with your actual Google OAuth client IDs
/// from Google Cloud Console (no Firebase needed).
class GoogleSignInConfig {
  // TODO: Replace with your actual Web Client ID from Google Cloud Console
  // This is found in Google Cloud Console > APIs & Services > Credentials
  // Create an OAuth 2.0 Client ID for "Web application"
  static const String webClientId = '229764348073-dkkcm16gb3avbrfqqfspurkr0ca8361e.apps.googleusercontent.com';

  // TODO: Replace with your actual iOS Client ID from Google Cloud Console
  // Create an OAuth 2.0 Client ID for "iOS application"
  // Bundle ID should be: com.myfamily.app
  static const String iosClientId = '229764348073-9tru16oc6501390g3cqo3pd61tshmvj7.apps.googleusercontent.com';

  // TODO: Replace with your actual Android Client ID from Google Cloud Console
  // Create an OAuth 2.0 Client ID for "Android application"
  // Package name should be: com.myfamily.app
  static const String androidClientId = '229764348073-9tru16oc6501390g3cqo3pd61tshmvj7.apps.googleusercontent.com';
  
  /// Scopes requested during Google Sign-In
  static const List<String> scopes = [
    'email',
    'profile',
  ];
  
  /// Whether to request server auth code (needed for backend authentication)
  static const bool requestServerAuthCode = true;
  
  /// Whether to force code for refresh token
  static const bool forceCodeForRefreshToken = true;
}
