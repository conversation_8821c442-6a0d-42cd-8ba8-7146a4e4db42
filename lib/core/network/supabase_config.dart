class SupabaseConfig {
  // Supabase project configuration
  static const String supabaseUrl = 'https://ovvathmpvaeeesmiywjb.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92dmF0aG1wdmFlZWVzbWl5d2piIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NDI3NjUsImV4cCI6MjA2NDQxODc2NX0.MYSVq47Zx0riqlptDGVTDP0QEX1lhecmdaQ56flZ-n4';
  
  // Auth configuration
  static const bool enableAutoRefreshToken = true;
  static const bool persistSession = true;
  static const bool detectSessionInUrl = true;
  
  // Database configuration
  static const String authTableName = 'auth.users';
  static const String profilesTableName = 'profiles';
  
  // Storage configuration
  static const String avatarsBucketName = 'avatars';
  static const String documentsBucketName = 'documents';
}
