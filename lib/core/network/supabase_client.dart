import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'supabase_config.dart';

/// Common Supabase network client for the entire application
/// This class provides a centralized way to interact with Supabase services
@singleton
class SupabaseNetworkClient {
  static SupabaseClient? _supabaseClient;

  SupabaseNetworkClient();

  /// Get the Supabase client instance
  SupabaseClient get client {
    if (_supabaseClient == null) {
      throw Exception('Supabase client not initialized. Call initialize() first.');
    }
    return _supabaseClient!;
  }

  /// Initialize Supabase client
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: SupabaseConfig.supabaseUrl,
      anonKey: SupabaseConfig.supabaseAnonKey,
      authOptions: const FlutterAuthClientOptions(
        authFlowType: AuthFlowType.pkce,
        autoRefreshToken: SupabaseConfig.enableAutoRefreshToken,
      ),
    );
    _supabaseClient = Supabase.instance.client;
  }

  /// Auth methods
  GoTrueClient get auth => client.auth;

  /// Database methods
  SupabaseClient get database => client;

  /// Storage methods
  SupabaseStorageClient get storage => client.storage;

  /// Realtime methods
  RealtimeClient get realtime => client.realtime;

  /// Check if user is authenticated
  bool get isAuthenticated => auth.currentUser != null;

  /// Get current user
  User? get currentUser => auth.currentUser;

  /// Get current session
  Session? get currentSession => auth.currentSession;

  /// Sign out current user
  Future<void> signOut() async {
    await auth.signOut();
  }

  /// Listen to auth state changes
  Stream<AuthState> get authStateChanges => auth.onAuthStateChange;

  /// Database query helpers
  
  /// Execute a query with error handling
  Future<T> executeQuery<T>(
    Future<T> Function() query, {
    String? errorMessage,
  }) async {
    try {
      return await query();
    } on PostgrestException catch (e) {
      throw SupabaseNetworkException(
        message: errorMessage ?? 'Database operation failed',
        code: e.code,
        details: e.details?.toString(),
        hint: e.hint,
      );
    } on AuthException catch (e) {
      throw SupabaseNetworkException(
        message: errorMessage ?? 'Authentication failed',
        code: e.statusCode,
        details: e.message,
      );
    } catch (e) {
      throw SupabaseNetworkException(
        message: errorMessage ?? 'Network operation failed',
        details: e.toString(),
      );
    }
  }

  /// Insert data into a table
  Future<List<Map<String, dynamic>>> insert(
    String table,
    Map<String, dynamic> data, {
    String? errorMessage,
  }) async {
    return executeQuery(
      () => database.from(table).insert(data).select(),
      errorMessage: errorMessage ?? 'Failed to insert data',
    );
  }

  /// Update data in a table
  Future<List<Map<String, dynamic>>> update(
    String table,
    Map<String, dynamic> data, {
    required String column,
    required dynamic value,
    String? errorMessage,
  }) async {
    return executeQuery(
      () => database.from(table).update(data).eq(column, value).select(),
      errorMessage: errorMessage ?? 'Failed to update data',
    );
  }

  /// Select data from a table
  Future<List<Map<String, dynamic>>> select(
    String table, {
    String columns = '*',
    String? where,
    dynamic whereValue,
    String? errorMessage,
  }) async {
    return executeQuery(
      () {
        var query = database.from(table).select(columns);
        if (where != null && whereValue != null) {
          query = query.eq(where, whereValue);
        }
        return query;
      },
      errorMessage: errorMessage ?? 'Failed to fetch data',
    );
  }

  /// Delete data from a table
  Future<List<Map<String, dynamic>>> delete(
    String table, {
    required String column,
    required dynamic value,
    String? errorMessage,
  }) async {
    return executeQuery(
      () => database.from(table).delete().eq(column, value).select(),
      errorMessage: errorMessage ?? 'Failed to delete data',
    );
  }
}

/// Custom exception for Supabase network operations
class SupabaseNetworkException implements Exception {
  final String message;
  final String? code;
  final String? details;
  final String? hint;

  const SupabaseNetworkException({
    required this.message,
    this.code,
    this.details,
    this.hint,
  });

  @override
  String toString() {
    var result = 'SupabaseNetworkException: $message';
    if (code != null) result += ' (Code: $code)';
    if (details != null) result += ' - $details';
    if (hint != null) result += ' Hint: $hint';
    return result;
  }
}
