// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:fam/core/network/supabase_client.dart' as _i573;
import 'package:fam/features/auth/domain/repository/auth_repository.dart'
    as _i975;
import 'package:fam/features/auth/domain/services/auth_service.dart' as _i287;
import 'package:fam/features/family/domain/repository/family_repository.dart'
    as _i818;
import 'package:fam/features/family/domain/repository/supabase_family_repository.dart'
    as _i591;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.singleton<_i573.SupabaseNetworkClient>(
        () => _i573.SupabaseNetworkClient());
    gh.factory<_i818.FamilyRepository>(() =>
        _i591.SupabaseFamilyRepository(gh<_i573.SupabaseNetworkClient>()));
    gh.factory<_i975.AuthRepository>(
        () => _i287.AuthService(gh<_i573.SupabaseNetworkClient>()));
    return this;
  }
}
