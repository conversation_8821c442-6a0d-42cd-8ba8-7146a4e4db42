import 'package:easy_localization/easy_localization.dart';

/// Extension to help with translation keys
extension TranslationHelper on String {
  /// Safely translate a string that might be a translation key or already translated text
  String safeTranslate() {
    // If the string starts with a translation key pattern, translate it
    if (startsWith('auth.') || 
        startsWith('common.') || 
        startsWith('dashboard.') ||
        startsWith('profile.') ||
        startsWith('family.') ||
        startsWith('events.') ||
        startsWith('memories.') ||
        startsWith('settings.')) {
      return tr(this);
    }
    // Otherwise, return the string as is (it's already translated or not a key)
    return this;
  }
}

/// Helper class for handling translation in different layers
class TranslationUtils {
  /// Translate a message that might be a translation key or plain text
  static String translateMessage(String? message) {
    if (message == null || message.isEmpty) {
      return '';
    }
    return message.safeTranslate();
  }

  /// Get a fallback translated message
  static String getTranslatedMessage(String? message, String fallbackKey) {
    if (message != null && message.isNotEmpty) {
      return message.safeTranslate();
    }
    return fallbackKey.tr();
  }
}
