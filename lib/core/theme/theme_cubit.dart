import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppThemeMode { light, dark, system }

class ThemeState {
  final AppThemeMode themeMode;
  final Brightness brightness;

  const ThemeState({
    required this.themeMode,
    required this.brightness,
  });

  ThemeState copyWith({
    AppThemeMode? themeMode,
    Brightness? brightness,
  }) {
    return ThemeState(
      themeMode: themeMode ?? this.themeMode,
      brightness: brightness ?? this.brightness,
    );
  }
}

class ThemeCubit extends Cubit<ThemeState> {
  static const String _themeKey = 'theme_mode';
  
  ThemeCubit() : super(const ThemeState(
    themeMode: AppThemeMode.light,
    brightness: Brightness.light,
  )) {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeIndex = prefs.getInt(_themeKey) ?? 0;
      final themeMode = AppThemeMode.values[themeIndex];

      final brightness = _getBrightnessForTheme(themeMode);

      emit(ThemeState(
        themeMode: themeMode,
        brightness: brightness,
      ));
    } catch (e) {
      // If loading fails, keep default light theme
      debugPrint('Failed to load theme: $e');
    }
  }

  Future<void> setTheme(AppThemeMode themeMode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, themeMode.index);

      final brightness = _getBrightnessForTheme(themeMode);

      emit(ThemeState(
        themeMode: themeMode,
        brightness: brightness,
      ));
    } catch (e) {
      debugPrint('Failed to save theme: $e');
    }
  }

  void toggleTheme() {
    final newTheme = state.themeMode == AppThemeMode.light
        ? AppThemeMode.dark
        : AppThemeMode.light;
    setTheme(newTheme);
  }

  Brightness _getBrightnessForTheme(AppThemeMode themeMode) {
    switch (themeMode) {
      case AppThemeMode.light:
        return Brightness.light;
      case AppThemeMode.dark:
        return Brightness.dark;
      case AppThemeMode.system:
        // For now, default to light. In a real app, you'd check system brightness
        return Brightness.light;
    }
  }

  bool get isDarkMode => state.brightness == Brightness.dark;
  bool get isLightMode => state.brightness == Brightness.light;
}
