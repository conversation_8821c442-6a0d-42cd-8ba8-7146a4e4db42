import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleState {
  final Locale locale;

  const LocaleState({required this.locale});

  LocaleState copyWith({Locale? locale}) {
    return LocaleState(locale: locale ?? this.locale);
  }
}

class LocaleCubit extends Cubit<LocaleState> {
  static const String _localeKey = 'app_locale';
  
  // Supported locales
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English
    Locale('ml', 'IN'), // Malayalam
  ];

  LocaleCubit() : super(const LocaleState(locale: Locale('en', 'US'))) {
    _loadLocale();
  }

  Future<void> _loadLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final localeCode = prefs.getString(_localeKey);
      
      if (localeCode != null) {
        final locale = _getLocaleFromCode(localeCode);
        emit(LocaleState(locale: locale));
      }
    } catch (e) {
      // If loading fails, keep default English locale
      debugPrint('Failed to load locale: $e');
    }
  }

  Future<void> setLocale(Locale locale) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_localeKey, locale.languageCode);
      
      emit(LocaleState(locale: locale));
    } catch (e) {
      debugPrint('Failed to save locale: $e');
    }
  }

  void toggleLanguage() {
    final newLocale = state.locale.languageCode == 'en' 
        ? const Locale('ml', 'IN')
        : const Locale('en', 'US');
    setLocale(newLocale);
  }

  Locale _getLocaleFromCode(String code) {
    switch (code) {
      case 'en':
        return const Locale('en', 'US');
      case 'ml':
        return const Locale('ml', 'IN');
      default:
        return const Locale('en', 'US');
    }
  }

  bool get isEnglish => state.locale.languageCode == 'en';
  bool get isMalayalam => state.locale.languageCode == 'ml';
  
  String get currentLanguageName {
    switch (state.locale.languageCode) {
      case 'en':
        return 'English';
      case 'ml':
        return 'മലയാളം';
      default:
        return 'English';
    }
  }
}
