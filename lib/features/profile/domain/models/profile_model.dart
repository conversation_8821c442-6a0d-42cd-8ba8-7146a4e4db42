import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class ProfileModel extends Equatable {
  final String id;
  final String name;
  final String email;
  final String? phoneNumber;
  final String? profileImageUrl;
  final String? location;
  final DateTime? dateOfBirth;
  final String? bio;
  final List<String> languages;
  final bool notificationsEnabled;
  final String theme;

  const ProfileModel({
    required this.id,
    required this.name,
    required this.email,
    this.phoneNumber,
    this.profileImageUrl,
    this.location,
    this.dateOfBirth,
    this.bio,
    this.languages = const ['English'],
    this.notificationsEnabled = true,
    this.theme = 'light',
  });

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phoneNumber,
        profileImageUrl,
        location,
        dateOfBirth,
        bio,
        languages,
        notificationsEnabled,
        theme,
      ];

  ProfileModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phoneNumber,
    String? profileImageUrl,
    String? location,
    DateTime? dateOfBirth,
    String? bio,
    List<String>? languages,
    bool? notificationsEnabled,
    String? theme,
  }) {
    return ProfileModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      location: location ?? this.location,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      bio: bio ?? this.bio,
      languages: languages ?? this.languages,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      theme: theme ?? this.theme,
    );
  }
}

class ProfileMenuItem extends Equatable {
  final String id;
  final String title;
  final String icon;
  final String? subtitle;
  final VoidCallback? onTap;

  const ProfileMenuItem({
    required this.id,
    required this.title,
    required this.icon,
    this.subtitle,
    this.onTap,
  });

  @override
  List<Object?> get props => [id, title, icon, subtitle];
}
