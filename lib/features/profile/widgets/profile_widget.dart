import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:google_fonts/google_fonts.dart';

class ProfileWidget extends StatelessWidget {
  const ProfileWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Profile Header
        _buildProfileHeader(),
        
        const SizedBox(height: 24),
        
        // Profile Stats
        _buildProfileStats(),
        
        const SizedBox(height: 24),
        
        // Menu Items
        _buildMenuItems(context),
      ],
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Profile Image
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: const Color(0xFFDBE8F2),
              border: Border.all(
                color: const Color(0xFFE8EDF5),
                width: 3,
              ),
            ),
            child: const Icon(
              Icons.person,
              size: 50,
              color: Color(0xFF4A739C),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Name
          Text(
            'Priya Nair',
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 24,
              color: const Color(0xFF0D141C),
            ),
          ),
          
          const SizedBox(height: 4),
          
          // Email
          Text(
            '<EMAIL>',
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w400,
              fontSize: 16,
              color: const Color(0xFF4A739C),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Location
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.location_on,
                size: 16,
                color: Color(0xFF4A739C),
              ),
              const SizedBox(width: 4),
              Text(
                'Kochi, Kerala',
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: const Color(0xFF4A739C),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProfileStats() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('profile.stats.familyMembers'.tr(), '24'),
          _buildStatDivider(),
          _buildStatItem('profile.stats.events'.tr(), '8'),
          _buildStatDivider(),
          _buildStatItem('profile.stats.photos'.tr(), '156'),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w700,
            fontSize: 20,
            color: const Color(0xFF0D141C),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w400,
            fontSize: 12,
            color: const Color(0xFF4A739C),
          ),
        ),
      ],
    );
  }

  Widget _buildStatDivider() {
    return Container(
      width: 1,
      height: 40,
      color: const Color(0xFFE8EDF5),
    );
  }

  Widget _buildMenuItems(BuildContext context) {
    final menuItems = [
      {
        'title': 'profile.editProfile'.tr(),
        'icon': Icons.edit,
        'onTap': () => _showComingSoon(context),
      },
      {
        'title': 'dashboard.familyTree'.tr(),
        'icon': Icons.account_tree,
        'onTap': () => _showComingSoon(context),
      },
      {
        'title': 'profile.myEvents'.tr(),
        'icon': Icons.event,
        'onTap': () => _showComingSoon(context),
      },
      {
        'title': 'profile.myPhotos'.tr(),
        'icon': Icons.photo_library,
        'onTap': () => _showComingSoon(context),
      },
      {
        'title': 'profile.privacySettings'.tr(),
        'icon': Icons.privacy_tip,
        'onTap': () => _showComingSoon(context),
      },
      {
        'title': 'profile.helpSupport'.tr(),
        'icon': Icons.help,
        'onTap': () => _showComingSoon(context),
      },
      {
        'title': 'profile.logout'.tr(),
        'icon': Icons.logout,
        'onTap': () => _showLogoutDialog(context),
      },
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: menuItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isLast = index == menuItems.length - 1;
          
          return Column(
            children: [
              ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: const Color(0xFFDBE8F2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    item['icon'] as IconData,
                    size: 20,
                    color: const Color(0xFF0D141C),
                  ),
                ),
                title: Text(
                  item['title'] as String,
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                    color: const Color(0xFF0D141C),
                  ),
                ),
                trailing: const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Color(0xFF4A739C),
                ),
                onTap: item['onTap'] as VoidCallback,
              ),
              if (!isLast)
                const Divider(
                  height: 1,
                  color: Color(0xFFE8EDF5),
                  indent: 72,
                ),
            ],
          );
        }).toList(),
      ),
    );
  }

  void _showComingSoon(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'common.comingSoon'.tr(),
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        backgroundColor: const Color(0xFF0D80F2),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'dialogs.logout.title'.tr(),
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color: const Color(0xFF0D141C),
            ),
          ),
          content: Text(
            'dialogs.logout.message'.tr(),
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w400,
              fontSize: 16,
              color: const Color(0xFF4A739C),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'dialogs.logout.cancel'.tr(),
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: const Color(0xFF4A739C),
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate back to welcome screen
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/',
                  (route) => false,
                );
              },
              child: Text(
                'dialogs.logout.confirm'.tr(),
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: Colors.red,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
