import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../widgets/profile_widget.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7FAFC),
      body: Safe<PERSON><PERSON>(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Profile',
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w700,
                        fontSize: 24,
                        color: const Color(0xFF0D141C),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        // Navigate to settings
                        Navigator.pushNamed(context, '/settings');
                      },
                      icon: const Icon(
                        Icons.settings,
                        color: Color(0xFF4A739C),
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Profile Content
              const ProfileWidget(),
              
              // Bottom spacing for navigation
              const SizedBox(height: 100),
            ],
          ),
        ),
      ),
    );
  }
}
