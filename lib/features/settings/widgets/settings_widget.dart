import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/theme/theme_cubit.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/localization/locale_cubit.dart';

class SettingsWidget extends StatefulWidget {
  const SettingsWidget({super.key});

  @override
  State<SettingsWidget> createState() => _SettingsWidgetState();
}

class _SettingsWidgetState extends State<SettingsWidget> {
  bool pushNotifications = true;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Language Section
          _buildSectionTitle('settings.language'.tr()),
          _buildLanguageOptions(),

          // Theme Section
          _buildSectionTitle('settings.theme'.tr()),
          _buildThemeOptions(),
          
          // Notifications Section
          _buildSectionTitle('settings.notifications'.tr()),
          _buildNotificationSettings(),

          // Account Section
          _buildSectionTitle('settings.account'.tr()),
          _buildAccountSettings(),
          
          // Bottom spacing
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: GoogleFonts.plusJakartaSans(
          fontWeight: FontWeight.w700,
          fontSize: 18,
          color: const Color(0xFF0D141C),
        ),
      ),
    );
  }

  Widget _buildLanguageOptions() {
    return BlocBuilder<LocaleCubit, LocaleState>(
      builder: (context, localeState) {
        final languages = [
          {'code': 'en', 'name': 'settings.languages.english'.tr()},
          {'code': 'ml', 'name': 'settings.languages.malayalam'.tr()},
        ];

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            children: languages.map((language) {
              final isSelected = language['code'] == localeState.locale.languageCode;
              return Expanded(
                child: GestureDetector(
                  onTap: () {
                    final newLocale = language['code'] == 'en'
                        ? const Locale('en', 'US')
                        : const Locale('ml', 'IN');
                    context.read<LocaleCubit>().setLocale(newLocale);
                    context.setLocale(newLocale);
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 12),
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : AppTheme.getCardColor(context),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppTheme.getDividerColor(context),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      language['name']!,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: isSelected
                            ? Colors.white
                            : Theme.of(context).textTheme.bodyLarge?.color,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  Widget _buildThemeOptions() {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final themes = [
          {'mode': AppThemeMode.light, 'name': 'settings.themes.light'.tr()},
          {'mode': AppThemeMode.dark, 'name': 'settings.themes.dark'.tr()},
        ];

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            children: themes.map((theme) {
              final isSelected = theme['mode'] == themeState.themeMode;
              return Expanded(
                child: GestureDetector(
                  onTap: () {
                    context.read<ThemeCubit>().setTheme(theme['mode'] as AppThemeMode);
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 12),
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : AppTheme.getCardColor(context),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppTheme.getDividerColor(context),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      theme['name'] as String,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: isSelected
                            ? Colors.white
                            : Theme.of(context).textTheme.bodyLarge?.color,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  Widget _buildNotificationSettings() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppTheme.getAccentColor(context),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              'settings.pushNotifications'.tr(),
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w400,
                fontSize: 16,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
          ),
          Container(
            width: 51,
            height: 31,
            decoration: BoxDecoration(
              color: pushNotifications
                  ? Theme.of(context).primaryColor
                  : AppTheme.getDividerColor(context),
              borderRadius: BorderRadius.circular(15.5),
            ),
            child: GestureDetector(
              onTap: () => setState(() => pushNotifications = !pushNotifications),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                alignment: pushNotifications ? Alignment.centerRight : Alignment.centerLeft,
                child: Container(
                  width: 27,
                  height: 27,
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(13.5),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.15),
                        blurRadius: 3,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSettings() {
    final accountItems = [
      {
        'title': 'settings.manageAccount'.tr(),
        'icon': Icons.person,
        'onTap': () => _showComingSoon('settings.manageAccount'.tr()),
      },
      {
        'title': 'profile.logout'.tr(),
        'icon': Icons.logout,
        'onTap': () => _showLogoutDialog(),
      },
    ];

    return Column(
      children: accountItems.map((item) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: AppTheme.getAccentColor(context),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.getAccentColor(context),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                item['icon'] as IconData,
                size: 20,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            title: Text(
              item['title'] as String,
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w400,
                fontSize: 16,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
            onTap: item['onTap'] as VoidCallback,
          ),
        );
      }).toList(),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '$feature - ${'common.comingSoon'.tr()}',
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        backgroundColor: Theme.of(context).primaryColor,
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'dialogs.logout.title'.tr(),
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          content: Text(
            'dialogs.logout.message'.tr(),
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w400,
              fontSize: 16,
              color: AppTheme.getSecondaryTextColor(context),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'dialogs.logout.cancel'.tr(),
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: AppTheme.getSecondaryTextColor(context),
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate back to welcome screen
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/',
                  (route) => false,
                );
              },
              child: Text(
                'dialogs.logout.confirm'.tr(),
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: Colors.red,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
