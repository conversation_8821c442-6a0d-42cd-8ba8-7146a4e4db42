import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:easy_localization/easy_localization.dart';
import '../logic/auth/auth_cubit.dart';
import '../logic/auth/auth_state.dart';

import '../domain/repository/auth_repository.dart';
import '../../dashboard/screens/dashboard_screen.dart';
import 'login_screen.dart';
import '../../../core/di/injection.dart';
import '../../../core/utils/translation_helper.dart';
import '../widgets/social_sign_in_buttons.dart';

class CreateAccountScreen extends StatelessWidget {
  const CreateAccountScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AuthCubit(
        getIt<AuthRepository>(),
      ),
      child: const CreateAccountView(),
    );
  }
}

class CreateAccountView extends StatefulWidget {
  const CreateAccountView({super.key});

  @override
  State<CreateAccountView> createState() => _CreateAccountViewState();
}

class _CreateAccountViewState extends State<CreateAccountView> {
  final _emailPhoneController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _emailPhoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      body: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) {
          if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message.safeTranslate()),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is AuthAccountCreated) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(TranslationUtils.getTranslatedMessage(state.message, 'auth.accountCreated')),
                backgroundColor: Colors.green,
              ),
            );
            // Navigate to dashboard screen
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const DashboardScreen(),
              ),
            );
          }
        },
        child: SafeArea(
          child: Column(
            children: [
              // Header with back button and title
              _buildHeader(context),
              
              // Main content
              Expanded(
                child: Column(
                  children: [
                    // Input section
                    _buildInputSection(),

                    // Spacer to push button to bottom
                    const Spacer(),

                    // Continue button and terms
                    _buildBottomSection(context),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button
          Container(
            width: 48,
            height: 48,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: Color(0xFF0F1417),
                size: 24,
              ),
            ),
          ),
          
          // Title
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(right: 48),
              child: Text(
                'auth.createAccount'.tr(),
                textAlign: TextAlign.center,
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w700,
                  fontSize: 18,
                  height: 1.28,
                  color: const Color(0xFF0F1417),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputSection() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: const Color(0xFFEBEDF2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextFormField(
                controller: _emailPhoneController,
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 16,
                  height: 1.5,
                  color: const Color(0xFF0F1417),
                ),
                decoration: InputDecoration(
                  hintText: 'auth.placeholders.phoneNumberOrEmail'.tr(),
                  hintStyle: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    height: 1.5,
                    color: const Color(0xFF5C738A),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                ),
                validator: (value) {
                  final error = context.read<AuthCubit>().validateEmailOrPhone(value ?? '');
                  return error?.safeTranslate();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSection(BuildContext context) {
    return Column(
      children: [
        // Continue button
        Container(
          padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
          child: BlocBuilder<AuthCubit, AuthState>(
            builder: (context, state) {
              final isLoading = state is AuthLoading;
              
              return SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: isLoading ? null : () => _handleContinue(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFDBE8F2),
                    foregroundColor: const Color(0xFF0F1417),
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0F1417)),
                          ),
                        )
                      : Text(
                          'auth.buttons.continue'.tr(),
                          style: GoogleFonts.plusJakartaSans(
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                            height: 1.5,
                          ),
                        ),
                ),
              );
            },
          ),
        ),

        // Divider
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 1,
                  color: Colors.grey.shade300,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  'auth.or'.tr(),
                  style: GoogleFonts.plusJakartaSans(
                    fontSize: 14,
                    color: const Color(0xFF5C738A),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  height: 1,
                  color: Colors.grey.shade300,
                ),
              ),
            ],
          ),
        ),

        // Social Sign-In Buttons
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: BlocBuilder<AuthCubit, AuthState>(
            builder: (context, state) {
              final isLoading = state is AuthLoading;

              return SocialSignInButtons(
                isLoading: isLoading,
                onGoogleSignIn: () => _handleGoogleSignIn(context),
                onAppleSignIn: () => _handleAppleSignIn(context),
              );
            },
          ),
        ),

        const SizedBox(height: 20),

        // Terms and conditions
        Container(
          padding: const EdgeInsets.fromLTRB(16, 4, 16, 12),
          child: Text(
            'auth.terms.termsAndConditions'.tr(),
            textAlign: TextAlign.center,
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              height: 1.5,
              color: const Color(0xFF5C738A),
            ),
          ),
        ),

        // Sign in link
        Container(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'auth.terms.alreadyHaveAccountQuestion'.tr(),
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  height: 1.5,
                  color: const Color(0xFF5C738A),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const LoginScreen(),
                    ),
                  );
                },
                child: Text(
                  'auth.signIn'.tr(),
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w700,
                    fontSize: 14,
                    height: 1.5,
                    color: const Color(0xFF0D80F2),
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // Bottom spacing
        const SizedBox(height: 20),
      ],
    );
  }

  void _handleContinue(BuildContext context) {
    if (_formKey.currentState?.validate() ?? false) {
      final emailOrPhone = _emailPhoneController.text.trim();
      context.read<AuthCubit>().createAccount(emailOrPhone);
    }
  }

  void _handleGoogleSignIn(BuildContext context) {
    context.read<AuthCubit>().signInWithGoogle();
  }

  void _handleAppleSignIn(BuildContext context) {
    context.read<AuthCubit>().signInWithApple();
  }
}
