import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:easy_localization/easy_localization.dart';
import '../logic/auth/auth_cubit.dart';
import '../logic/auth/auth_state.dart';
import '../domain/repository/auth_repository.dart';
import '../../dashboard/screens/dashboard_screen.dart';
import '../../../core/di/injection.dart';
import '../../../core/utils/translation_helper.dart';
import '../widgets/social_sign_in_buttons.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AuthCubit(
        getIt<AuthRepository>(),
      ),
      child: const LoginView(),
    );
  }
}

class LoginView extends StatefulWidget {
  const LoginView({super.key});

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  final _emailPhoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailPhoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      body: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) {
          if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message.safeTranslate()),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is AuthSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(TranslationUtils.getTranslatedMessage(state.message, 'auth.messages.welcomeBack')),
                backgroundColor: Colors.green,
              ),
            );
            // Navigate to dashboard screen with user data
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => DashboardScreen(user: state.user),
              ),
            );
          }
        },
        child: SafeArea(
          child: Column(
            children: [
              // Header with back button and title
              _buildHeader(context),
              
              // Main content
              Expanded(
                child: Column(
                  children: [
                    // Input section
                    _buildInputSection(),

                    // Spacer to push button to bottom
                    const Spacer(),

                    // Sign in button
                    _buildBottomSection(context),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button
          Container(
            width: 48,
            height: 48,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: Color(0xFF0F1417),
                size: 24,
              ),
            ),
          ),
          
          // Title
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(right: 48),
              child: Text(
                'auth.signIn'.tr(),
                textAlign: TextAlign.center,
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w700,
                  fontSize: 18,
                  height: 1.28,
                  color: const Color(0xFF0F1417),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputSection() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            // Email/Phone field
            Container(
              decoration: BoxDecoration(
                color: const Color(0xFFEBEDF2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextFormField(
                controller: _emailPhoneController,
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 16,
                  height: 1.5,
                  color: const Color(0xFF0F1417),
                ),
                decoration: InputDecoration(
                  hintText: 'auth.placeholders.phoneNumberOrEmail'.tr(),
                  hintStyle: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    height: 1.5,
                    color: const Color(0xFF5C738A),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                ),
                validator: (value) {
                  final error = context.read<AuthCubit>().validateEmailOrPhone(value ?? '');
                  return error?.safeTranslate();
                },
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Password field
            Container(
              decoration: BoxDecoration(
                color: const Color(0xFFEBEDF2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextFormField(
                controller: _passwordController,
                obscureText: _obscurePassword,
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 16,
                  height: 1.5,
                  color: const Color(0xFF0F1417),
                ),
                decoration: InputDecoration(
                  hintText: 'auth.placeholders.password'.tr(),
                  hintStyle: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    height: 1.5,
                    color: const Color(0xFF5C738A),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  suffixIcon: IconButton(
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                    icon: Icon(
                      _obscurePassword ? Icons.visibility : Icons.visibility_off,
                      color: const Color(0xFF5C738A),
                    ),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'auth.validation.passwordRequired'.tr();
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSection(BuildContext context) {
    return Column(
      children: [
        // Sign in button
        Container(
          padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
          child: BlocBuilder<AuthCubit, AuthState>(
            builder: (context, state) {
              final isLoading = state is AuthLoading;
              
              return SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: isLoading ? null : () => _handleSignIn(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFDBE8F2),
                    foregroundColor: const Color(0xFF0F1417),
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0F1417)),
                          ),
                        )
                      : Text(
                          'auth.signIn'.tr(),
                          style: GoogleFonts.plusJakartaSans(
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                            height: 1.5,
                          ),
                        ),
                ),
              );
            },
          ),
        ),

        // Divider
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 1,
                  color: Colors.grey.shade300,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  'auth.or'.tr(),
                  style: GoogleFonts.plusJakartaSans(
                    fontSize: 14,
                    color: const Color(0xFF5C738A),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  height: 1,
                  color: Colors.grey.shade300,
                ),
              ),
            ],
          ),
        ),

        // Social Sign-In Buttons
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: BlocBuilder<AuthCubit, AuthState>(
            builder: (context, state) {
              final isLoading = state is AuthLoading;

              return SocialSignInButtons(
                isLoading: isLoading,
                onGoogleSignIn: () => _handleGoogleSignIn(context),
                onAppleSignIn: () => _handleAppleSignIn(context),
              );
            },
          ),
        ),

        // Bottom spacing
        const SizedBox(height: 20),
      ],
    );
  }

  void _handleSignIn(BuildContext context) {
    if (_formKey.currentState?.validate() ?? false) {
      final emailOrPhone = _emailPhoneController.text.trim();
      final password = _passwordController.text;
      context.read<AuthCubit>().signIn(emailOrPhone, password);
    }
  }

  void _handleGoogleSignIn(BuildContext context) {
    context.read<AuthCubit>().signInWithGoogle();
  }

  void _handleAppleSignIn(BuildContext context) {
    context.read<AuthCubit>().signInWithApple();
  }
}
