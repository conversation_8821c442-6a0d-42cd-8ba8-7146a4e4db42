import 'package:equatable/equatable.dart';

class AuthModel extends Equatable {
  final String? id;
  final String email;
  final String? phoneNumber;
  final String? name;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final DateTime? createdAt;

  const AuthModel({
    this.id,
    required this.email,
    this.phoneNumber,
    this.name,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.createdAt,
  });

  @override
  List<Object?> get props => [
        id,
        email,
        phoneNumber,
        name,
        isEmailVerified,
        isPhoneVerified,
        createdAt,
      ];

  AuthModel copyWith({
    String? id,
    String? email,
    String? phoneNumber,
    String? name,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    DateTime? createdAt,
  }) {
    return AuthModel(
      id: id ?? this.id,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      name: name ?? this.name,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class CreateAccountRequest extends Equatable {
  final String emailOrPhone;
  final bool isEmail;

  const CreateAccountRequest({
    required this.emailOrPhone,
    required this.isEmail,
  });

  @override
  List<Object?> get props => [emailOrPhone, isEmail];
}

class AuthResponse extends Equatable {
  final bool success;
  final String? message;
  final AuthModel? user;
  final String? token;

  const AuthResponse({
    required this.success,
    this.message,
    this.user,
    this.token,
  });

  @override
  List<Object?> get props => [success, message, user, token];
}
