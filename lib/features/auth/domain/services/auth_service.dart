import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthResponse;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import '../models/auth_model.dart';
import '../repository/auth_repository.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/config/google_signin_config.dart';

@Injectable(as: AuthRepository)
class AuthService implements AuthRepository {
  final SupabaseNetworkClient _networkClient;

  AuthService(this._networkClient);

  /// Validate email format
  @override
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validate phone number format (Indian format)
  @override
  bool isValidPhoneNumber(String phone) {
    // Remove any spaces, dashes, or parentheses
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // Check for Indian phone number format
    return RegExp(r'^(\+91|91)?[6-9]\d{9}$').hasMatch(cleanPhone);
  }

  /// Determine if input is email or phone number
  @override
  bool isEmailInput(String input) {
    return input.contains('@') && isValidEmail(input);
  }

  /// Validate input (email or phone)
  @override
  String? validateEmailOrPhone(String input) {
    if (input.trim().isEmpty) {
      return 'auth.validation.emailOrPhoneRequired';
    }

    return input.contains('@')
        ? (!isValidEmail(input) ? 'auth.validation.validEmailRequired' : null)
        : (!isValidPhoneNumber(input) ? 'auth.validation.validPhoneRequired' : null);
  }

  /// Create account with validation
  @override
  Future<AuthResponse> createAccount(CreateAccountRequest request) async {
    final validationError = validateEmailOrPhone(request.emailOrPhone);
    if (validationError != null) {
      return AuthResponse(success: false, message: validationError);
    }

    return request.isEmail
        ? _signUpWithEmail(request.emailOrPhone.trim())
        : _signUpWithPhone(request.emailOrPhone.trim());
  }



  /// Sign in with validation
  @override
  Future<AuthResponse> signIn(String emailOrPhone, String password) async {
    final validationError = validateEmailOrPhone(emailOrPhone);
    if (validationError != null) {
      return AuthResponse(success: false, message: validationError);
    }

    if (password.isEmpty) {
      return const AuthResponse(success: false, message: 'auth.validation.passwordRequired');
    }

    return emailOrPhone.contains('@')
        ? _signInWithEmail(emailOrPhone.trim(), password)
        : _signInWithPhone(emailOrPhone.trim(), password);
  }

  /// Sign in with Google
  @override
  Future<AuthResponse> signInWithGoogle() async {
    try {
      // Initialize Google Sign-In with configuration
      final GoogleSignIn googleSignIn = GoogleSignIn(
        scopes: GoogleSignInConfig.scopes,
        // Only set serverClientId for web authentication with backend
        serverClientId: GoogleSignInConfig.webClientId,
      );

      final googleUser = await googleSignIn.signIn();
      if (googleUser == null) {
        return const AuthResponse(
          success: false,
          message: 'auth.messages.googleSignInCancelled',
        );
      }

      final googleAuth = await googleUser.authentication;
      final accessToken = googleAuth.accessToken;
      final idToken = googleAuth.idToken;

      if (accessToken == null) {
        return const AuthResponse(
          success: false,
          message: 'auth.messages.googleSignInFailed',
        );
      }

      // Sign in with Supabase using Google credentials
      final response = await _networkClient.auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: idToken!,
        accessToken: accessToken,
      );

      if (response.user != null) {
        return AuthResponse(
          success: true,
          message: 'auth.messages.googleSignInSuccess',
          user: _mapUserToAuthModel(response.user!),
          token: response.session?.accessToken,
        );
      } else {
        return const AuthResponse(
          success: false,
          message: 'auth.messages.googleSignInFailed',
        );
      }
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'auth.messages.googleSignInError: ${e.toString()}',
      );
    }
  }

  /// Sign in with Apple (iOS only)
  @override
  Future<AuthResponse> signInWithApple() async {
    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final idToken = credential.identityToken;
      if (idToken == null) {
        return const AuthResponse(
          success: false,
          message: 'auth.messages.appleSignInFailed',
        );
      }

      // Sign in with Supabase using Apple credentials
      final response = await _networkClient.auth.signInWithIdToken(
        provider: OAuthProvider.apple,
        idToken: idToken,
      );

      if (response.user != null) {
        return AuthResponse(
          success: true,
          message: 'auth.messages.appleSignInSuccess',
          user: _mapUserToAuthModel(response.user!),
          token: response.session?.accessToken,
        );
      } else {
        return const AuthResponse(
          success: false,
          message: 'auth.messages.appleSignInFailed',
        );
      }
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'auth.messages.appleSignInError: ${e.toString()}',
      );
    }
  }

  /// Get current user
  @override
  Future<AuthModel?> getCurrentUser() async {
    try {
      final user = _networkClient.currentUser;
      if (user == null) return null;

      return _mapUserToAuthModel(user);
    } catch (e) {
      return null;
    }
  }

  /// Check authentication status
  @override
  Future<bool> isAuthenticated() async {
    try {
      return _networkClient.isAuthenticated;
    } catch (e) {
      return false;
    }
  }

  /// Sign out
  @override
  Future<void> signOut() async {
    try {
      await _networkClient.signOut();
    } catch (e) {
      // Handle sign out error silently
    }
  }

  /// Send verification code to email or phone
  @override
  Future<AuthResponse> sendVerificationCode(String emailOrPhone) async {
    try {
      if (emailOrPhone.contains('@')) {
        // Send email verification
        await _networkClient.auth.resend(
          type: OtpType.signup,
          email: emailOrPhone,
        );
      } else {
        // Send SMS verification
        await _networkClient.auth.resend(
          type: OtpType.sms,
          phone: emailOrPhone,
        );
      }

      return const AuthResponse(
        success: true,
        message: 'auth.messages.verificationCodeSent',
      );
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'auth.messages.failedToSendVerificationCode',
      );
    }
  }

  /// Verify code sent to email or phone
  @override
  Future<AuthResponse> verifyCode(String emailOrPhone, String code) async {
    try {
      final AuthResponse response;

      if (emailOrPhone.contains('@')) {
        // Verify email OTP
        response = await _verifyEmailOTP(emailOrPhone, code);
      } else {
        // Verify SMS OTP
        response = await _verifySMSOTP(emailOrPhone, code);
      }

      return response;
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'auth.messages.failedToVerifyCode',
      );
    }
  }

  /// Reset password
  @override
  Future<AuthResponse> resetPassword(String emailOrPhone) async {
    try {
      if (emailOrPhone.contains('@')) {
        // Reset password with email
        await _networkClient.auth.resetPasswordForEmail(emailOrPhone);

        return const AuthResponse(
          success: true,
          message: 'auth.messages.passwordResetEmailSent',
        );
      } else {
        // For phone numbers, we might need to implement a different flow
        return const AuthResponse(
          success: false,
          message: 'auth.messages.passwordResetPhoneNotSupported',
        );
      }
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'auth.messages.failedToResetPassword',
      );
    }
  }

  /// Update user profile
  @override
  Future<AuthResponse> updateProfile(AuthModel user) async {
    try {
      final updates = <String, dynamic>{};

      if (user.name != null) {
        updates['name'] = user.name;
      }

      // Update user metadata
      final response = await _networkClient.auth.updateUser(
        UserAttributes(
          data: updates,
        ),
      );

      if (response.user != null) {
        return AuthResponse(
          success: true,
          message: 'auth.messages.profileUpdatedSuccess',
          user: _mapUserToAuthModel(response.user!),
        );
      } else {
        return const AuthResponse(
          success: false,
          message: 'auth.messages.failedToUpdateProfile',
        );
      }
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'auth.messages.failedToUpdateProfile',
      );
    }
  }

  /// Delete user account
  @override
  Future<AuthResponse> deleteAccount() async {
    try {
      // Note: Supabase doesn't have a direct delete user method in the client
      // This would typically be handled by a server-side function or admin API
      // For now, we'll just sign out the user
      await _networkClient.signOut();

      return const AuthResponse(
        success: true,
        message: 'auth.messages.accountDeletionInitiated',
      );
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'auth.messages.failedToDeleteAccount',
      );
    }
  }

  // Private helper methods

  Future<AuthResponse> _signUpWithEmail(String email) async {
    try {
      // Generate a default password for email signup
      const defaultPassword = 'TempPass123!';

      final response = await _networkClient.auth.signUp(
        email: email,
        password: defaultPassword,
      );

      if (response.user != null) {
        return AuthResponse(
          success: true,
          message: 'auth.messages.accountCreatedSuccess',
          user: _mapUserToAuthModel(response.user!),
          token: response.session?.accessToken,
        );
      } else {
        return const AuthResponse(
          success: false,
          message: 'auth.messages.failedToCreateAccount',
        );
      }
    } on AuthException catch (e) {
      return AuthResponse(
        success: false,
        message: e.message,
      );
    }
  }

  Future<AuthResponse> _signUpWithPhone(String phone) async {
    try {
      // Generate a default password for phone signup
      const defaultPassword = 'TempPass123!';

      final response = await _networkClient.auth.signUp(
        phone: phone,
        password: defaultPassword,
      );

      if (response.user != null) {
        return AuthResponse(
          success: true,
          message: 'auth.messages.accountCreatedPhoneSuccess',
          user: _mapUserToAuthModel(response.user!),
          token: response.session?.accessToken,
        );
      } else {
        return const AuthResponse(
          success: false,
          message: 'auth.messages.failedToCreateAccount',
        );
      }
    } on AuthException catch (e) {
      return AuthResponse(
        success: false,
        message: e.message,
      );
    }
  }

  Future<AuthResponse> _signInWithEmail(String email, String password) async {
    try {
      // Try with provided password first
      var response = await _networkClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        return AuthResponse(
          success: true,
          message: 'auth.messages.welcomeBack',
          user: _mapUserToAuthModel(response.user!),
          token: response.session?.accessToken,
        );
      }

      return const AuthResponse(
        success: false,
        message: 'auth.messages.invalidCredentials',
      );
    } on AuthException catch (e) {
      // If provided password fails, try default password
      try {
        const defaultPassword = 'TempPass123!';
        final response = await _networkClient.auth.signInWithPassword(
          email: email,
          password: defaultPassword,
        );

        if (response.user != null) {
          return AuthResponse(
            success: true,
            message: 'auth.messages.welcomeBack',
            user: _mapUserToAuthModel(response.user!),
            token: response.session?.accessToken,
          );
        }
      } on AuthException {
        // Both passwords failed
      }

      return AuthResponse(
        success: false,
        message: e.message,
      );
    }
  }

  Future<AuthResponse> _signInWithPhone(String phone, String password) async {
    try {
      // Try with provided password first
      var response = await _networkClient.auth.signInWithPassword(
        phone: phone,
        password: password,
      );

      if (response.user != null) {
        return AuthResponse(
          success: true,
          message: 'auth.messages.welcomeBack',
          user: _mapUserToAuthModel(response.user!),
          token: response.session?.accessToken,
        );
      }

      return const AuthResponse(
        success: false,
        message: 'auth.messages.invalidCredentials',
      );
    } on AuthException catch (e) {
      // If provided password fails, try default password
      try {
        const defaultPassword = 'TempPass123!';
        final response = await _networkClient.auth.signInWithPassword(
          phone: phone,
          password: defaultPassword,
        );

        if (response.user != null) {
          return AuthResponse(
            success: true,
            message: 'auth.messages.welcomeBack',
            user: _mapUserToAuthModel(response.user!),
            token: response.session?.accessToken,
          );
        }
      } on AuthException {
        // Both passwords failed
      }

      return AuthResponse(
        success: false,
        message: e.message,
      );
    }
  }

  Future<AuthResponse> _verifyEmailOTP(String email, String code) async {
    try {
      final response = await _networkClient.auth.verifyOTP(
        email: email,
        token: code,
        type: OtpType.signup,
      );

      if (response.user != null) {
        return AuthResponse(
          success: true,
          message: 'auth.messages.emailVerifiedSuccess',
          user: _mapUserToAuthModel(response.user!),
          token: response.session?.accessToken,
        );
      } else {
        return const AuthResponse(
          success: false,
          message: 'auth.messages.invalidVerificationCode',
        );
      }
    } on AuthException catch (e) {
      return AuthResponse(
        success: false,
        message: e.message,
      );
    }
  }

  Future<AuthResponse> _verifySMSOTP(String phone, String code) async {
    try {
      final response = await _networkClient.auth.verifyOTP(
        phone: phone,
        token: code,
        type: OtpType.sms,
      );

      if (response.user != null) {
        return AuthResponse(
          success: true,
          message: 'auth.messages.phoneVerifiedSuccess',
          user: _mapUserToAuthModel(response.user!),
          token: response.session?.accessToken,
        );
      } else {
        return const AuthResponse(
          success: false,
          message: 'auth.messages.invalidVerificationCode',
        );
      }
    } on AuthException catch (e) {
      return AuthResponse(
        success: false,
        message: e.message,
      );
    }
  }

  AuthModel _mapUserToAuthModel(User user) {
    return AuthModel(
      id: user.id,
      email: user.email ?? '',
      phoneNumber: user.phone,
      name: user.userMetadata?['name'] as String?,
      isEmailVerified: user.emailConfirmedAt != null,
      isPhoneVerified: user.phoneConfirmedAt != null,
      createdAt: DateTime.tryParse(user.createdAt),
    );
  }
}
