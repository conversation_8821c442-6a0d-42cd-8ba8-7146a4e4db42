import '../models/auth_model.dart';

/// Abstract repository interface for authentication operations
/// This defines the contract that any auth repository implementation must follow
abstract class AuthRepository {
  /// Create a new account with email or phone number
  Future<AuthResponse> createAccount(CreateAccountRequest request);

  /// Sign in with email or phone number
  Future<AuthResponse> signIn(String emailOrPhone, String password);

  /// Sign in with Google
  Future<AuthResponse> signInWithGoogle();

  /// Sign in with Apple (iOS only)
  Future<AuthResponse> signInWithApple();

  /// Sign out current user
  Future<void> signOut();

  /// Get current authenticated user
  Future<AuthModel?> getCurrentUser();

  /// Check if user is authenticated
  Future<bool> isAuthenticated();

  /// Send verification code to email or phone
  Future<AuthResponse> sendVerificationCode(String emailOrPhone);

  /// Verify code sent to email or phone
  Future<AuthResponse> verifyCode(String emailOrPhone, String code);

  /// Reset password
  Future<AuthResponse> resetPassword(String emailOrPhone);

  /// Update user profile
  Future<AuthResponse> updateProfile(AuthModel user);

  /// Delete user account
  Future<AuthResponse> deleteAccount();

  // Validation methods
  /// Validate email format
  bool isValidEmail(String email);

  /// Validate phone number format
  bool isValidPhoneNumber(String phone);

  /// Determine if input is email or phone number
  bool isEmailInput(String input);

  /// Validate input (email or phone)
  String? validateEmailOrPhone(String input);
}
