import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/repository/auth_repository.dart';
import '../../domain/models/auth_model.dart';
import 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final AuthRepository _authRepository;

  AuthCubit(this._authRepository) : super(const AuthInitial());

  /// Create a new account
  Future<void> createAccount(String emailOrPhone) async {
    try {
      emit(const AuthLoading());

      // Use service for validation, repository for actual operation
      final validationError = _authRepository.validateEmailOrPhone(emailOrPhone);
      if (validationError != null) {
        emit(AuthError(message: validationError));
        return;
      }

      final request = CreateAccountRequest(
        emailOrPhone: emailOrPhone.trim(),
        isEmail: _authRepository.isEmailInput(emailOrPhone),
      );

      final response = await _authRepository.createAccount(request);

      if (response.success) {
        emit(AuthAccountCreated(
          emailOrPhone: emailOrPhone,
          isEmail: _authRepository.isEmailInput(emailOrPhone),
          message: response.message,
        ));
      } else {
        emit(AuthError(message: response.message ?? 'auth.messages.failedToCreateAccount'));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Sign in with email/phone and password
  Future<void> signIn(String emailOrPhone, String password) async {
    try {
      emit(const AuthLoading());

      // Use service for validation
      final validationError = _authRepository.validateEmailOrPhone(emailOrPhone);
      if (validationError != null) {
        emit(AuthError(message: validationError));
        return;
      }

      if (password.isEmpty) {
        emit(const AuthError(message: 'auth.validation.passwordRequired'));
        return;
      }

      final response = await _authRepository.signIn(emailOrPhone, password);

      if (response.success && response.user != null) {
        emit(AuthSuccess(
          user: response.user!,
          message: response.message,
        ));
      } else {
        emit(AuthError(message: response.message ?? 'auth.messages.failedToSignIn'));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Sign in with Google
  Future<void> signInWithGoogle() async {
    try {
      emit(const AuthLoading());

      final response = await _authRepository.signInWithGoogle();

      if (response.success && response.user != null) {
        emit(AuthSuccess(
          user: response.user!,
          message: response.message,
        ));
      } else {
        emit(AuthError(message: response.message ?? 'auth.messages.googleSignInFailed'));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Sign in with Apple (iOS only)
  Future<void> signInWithApple() async {
    try {
      emit(const AuthLoading());

      final response = await _authRepository.signInWithApple();

      if (response.success && response.user != null) {
        emit(AuthSuccess(
          user: response.user!,
          message: response.message,
        ));
      } else {
        emit(AuthError(message: response.message ?? 'auth.messages.appleSignInFailed'));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    try {
      emit(const AuthLoading());
      await _authRepository.signOut();
      emit(const AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Check current authentication status
  Future<void> checkAuthStatus() async {
    try {
      emit(const AuthLoading());

      final isAuthenticated = await _authRepository.isAuthenticated();

      if (isAuthenticated) {
        final user = await _authRepository.getCurrentUser();
        if (user != null) {
          emit(AuthSuccess(user: user));
        } else {
          emit(const AuthUnauthenticated());
        }
      } else {
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      emit(const AuthUnauthenticated());
    }
  }

  /// Reset to initial state
  void reset() {
    emit(const AuthInitial());
  }

  /// Clear error state
  void clearError() {
    if (state is AuthError) {
      emit(const AuthInitial());
    }
  }

  /// Validate email or phone input
  String? validateEmailOrPhone(String input) {
    return _authRepository.validateEmailOrPhone(input);
  }

  /// Check if input is email
  bool isEmailInput(String input) {
    return _authRepository.isEmailInput(input);
  }
}
