import 'package:equatable/equatable.dart';
import '../../domain/models/auth_model.dart';

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class AuthSuccess extends AuthState {
  final AuthModel user;
  final String? message;

  const AuthSuccess({
    required this.user,
    this.message,
  });

  @override
  List<Object?> get props => [user, message];
}

class AuthError extends AuthState {
  final String message;

  const AuthError({required this.message});

  @override
  List<Object?> get props => [message];
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

class AuthAccountCreated extends AuthState {
  final String emailOrPhone;
  final bool isEmail;
  final String? message;

  const AuthAccountCreated({
    required this.emailOrPhone,
    required this.isEmail,
    this.message,
  });

  @override
  List<Object?> get props => [emailOrPhone, isEmail, message];
}

class AuthVerificationSent extends AuthState {
  final String emailOrPhone;
  final bool isEmail;

  const AuthVerificationSent({
    required this.emailOrPhone,
    required this.isEmail,
  });

  @override
  List<Object?> get props => [emailOrPhone, isEmail];
}

class AuthVerificationSuccess extends AuthState {
  final AuthModel user;

  const AuthVerificationSuccess({required this.user});

  @override
  List<Object?> get props => [user];
}

class AuthPasswordResetSent extends AuthState {
  final String emailOrPhone;

  const AuthPasswordResetSent({required this.emailOrPhone});

  @override
  List<Object?> get props => [emailOrPhone];
}
