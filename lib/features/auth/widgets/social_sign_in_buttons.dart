import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:easy_localization/easy_localization.dart';

class SocialSignInButtons extends StatelessWidget {
  final VoidCallback? onGoogleSignIn;
  final VoidCallback? onAppleSignIn;
  final bool isLoading;

  const SocialSignInButtons({
    super.key,
    this.onGoogleSignIn,
    this.onAppleSignIn,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Google Sign-In Button
        _SocialSignInButton(
          onPressed: isLoading ? null : onGoogleSignIn,
          icon: _GoogleIcon(),
          text: 'auth.buttons.signInWithGoogle'.tr(),
          backgroundColor: Colors.white,
          textColor: Colors.black87,
          borderColor: Colors.grey.shade300,
        ),

        const SizedBox(height: 12),

        // Apple Sign-In Button (iOS only)
        if (!kIsWeb && defaultTargetPlatform == TargetPlatform.iOS)
          _SocialSignInButton(
            onPressed: isLoading ? null : onAppleSignIn,
            icon: _AppleIcon(),
            text: 'auth.buttons.signInWithApple'.tr(),
            backgroundColor: Colors.black,
            textColor: Colors.white,
            borderColor: Colors.black,
          ),
      ],
    );
  }
}

class _SocialSignInButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget icon;
  final String text;
  final Color backgroundColor;
  final Color textColor;
  final Color borderColor;

  const _SocialSignInButton({
    required this.onPressed,
    required this.icon,
    required this.text,
    required this.backgroundColor,
    required this.textColor,
    required this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          backgroundColor: backgroundColor,
          side: BorderSide(color: borderColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon,
            const SizedBox(width: 12),
            Text(
              text,
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: textColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _GoogleIcon extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 24,
      height: 24,
      child: CustomPaint(
        painter: _GoogleIconPainter(),
      ),
    );
  }
}

class _GoogleIconPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Google "G" colors
    final bluePaint = Paint()..color = const Color(0xFF4285F4);
    final redPaint = Paint()..color = const Color(0xFFEA4335);
    final yellowPaint = Paint()..color = const Color(0xFFFBBC05);
    final greenPaint = Paint()..color = const Color(0xFF34A853);

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw Google "G" icon (simplified version)
    // Blue section
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius * 0.8),
      -1.57, // -90 degrees
      1.57, // 90 degrees
      false,
      bluePaint..strokeWidth = radius * 0.3..style = PaintingStyle.stroke,
    );

    // Red section
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius * 0.8),
      0, // 0 degrees
      1.57, // 90 degrees
      false,
      redPaint..strokeWidth = radius * 0.3..style = PaintingStyle.stroke,
    );

    // Yellow section
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius * 0.8),
      1.57, // 90 degrees
      1.57, // 90 degrees
      false,
      yellowPaint..strokeWidth = radius * 0.3..style = PaintingStyle.stroke,
    );

    // Green section
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius * 0.8),
      3.14, // 180 degrees
      1.57, // 90 degrees
      false,
      greenPaint..strokeWidth = radius * 0.3..style = PaintingStyle.stroke,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _AppleIcon extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Icon(
      Icons.apple,
      size: 24,
      color: Colors.white,
    );
  }
}
