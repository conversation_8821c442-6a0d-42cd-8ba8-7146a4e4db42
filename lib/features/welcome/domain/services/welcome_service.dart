import '../models/welcome_model.dart';
import '../repository/welcome_repository.dart';

class WelcomeService {
  final WelcomeRepository _repository;

  WelcomeService(this._repository);

  /// Get default welcome configuration
  WelcomeModel getDefaultConfig() {
    return const WelcomeModel(
      title: 'Welcome to the Kerala Family App',
      subtitle: 'Connect with your family, preserve your heritage, and celebrate traditions.',
      backgroundImagePath: 'assets/images/welcome_background.png',
    );
  }

  /// Load welcome configuration from repository
  Future<WelcomeModel> loadWelcomeConfig() async {
    try {
      return await _repository.getWelcomeConfig();
    } catch (e) {
      // Return default config if loading fails
      return getDefaultConfig();
    }
  }

  /// Handle button actions
  Future<void> handleButtonAction(String action) async {
    switch (action) {
      case 'navigate_to_main':
        await _repository.markWelcomeCompleted();
        // Navigation logic will be handled by the UI layer
        break;
      case 'navigate_to_learn_more':
        // Learn more logic will be handled by the UI layer
        break;
      default:
        throw UnimplementedError('Action $action not implemented');
    }
  }

  /// Check if welcome should be shown
  Future<bool> shouldShowWelcome() async {
    return !(await _repository.hasCompletedWelcome());
  }
}
