import '../models/welcome_model.dart';

abstract class WelcomeRepository {
  /// Get welcome screen configuration
  Future<WelcomeModel> getWelcomeConfig();
  
  /// Update welcome screen configuration
  Future<void> updateWelcomeConfig(WelcomeModel config);
  
  /// Check if user has completed welcome flow
  Future<bool> hasCompletedWelcome();
  
  /// Mark welcome flow as completed
  Future<void> markWelcomeCompleted();
  
  /// Reset welcome flow status
  Future<void> resetWelcomeStatus();
}
