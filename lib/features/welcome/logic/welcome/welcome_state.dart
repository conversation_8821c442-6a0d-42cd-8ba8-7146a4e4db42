import 'package:equatable/equatable.dart';
import '../../domain/models/welcome_model.dart';

abstract class WelcomeState extends Equatable {
  const WelcomeState();

  @override
  List<Object?> get props => [];
}

class WelcomeInitial extends WelcomeState {
  const WelcomeInitial();
}

class WelcomeLoading extends WelcomeState {
  const WelcomeLoading();
}

class WelcomeLoaded extends WelcomeState {
  final WelcomeModel config;

  const WelcomeLoaded({required this.config});

  @override
  List<Object?> get props => [config];
}

class WelcomeError extends WelcomeState {
  final String message;

  const WelcomeError({required this.message});

  @override
  List<Object?> get props => [message];
}

class WelcomeButtonPressed extends WelcomeState {
  final String action;

  const WelcomeButtonPressed({required this.action});

  @override
  List<Object?> get props => [action];
}

class WelcomeCompleted extends WelcomeState {
  const WelcomeCompleted();
}
