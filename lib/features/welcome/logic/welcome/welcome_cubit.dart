import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/services/welcome_service.dart';
import 'welcome_state.dart';

class WelcomeCubit extends Cubit<WelcomeState> {
  final WelcomeService _welcomeService;

  WelcomeCubit(this._welcomeService) : super(const WelcomeInitial());

  /// Load welcome configuration
  Future<void> loadWelcomeConfig() async {
    try {
      emit(const WelcomeLoading());
      final config = await _welcomeService.loadWelcomeConfig();
      emit(WelcomeLoaded(config: config));
    } catch (e) {
      emit(WelcomeError(message: e.toString()));
    }
  }

  /// Handle button press
  Future<void> onButtonPressed(String action) async {
    try {
      emit(WelcomeButtonPressed(action: action));
      await _welcomeService.handleButtonAction(action);
      
      if (action == 'navigate_to_main') {
        emit(const WelcomeCompleted());
      }
    } catch (e) {
      emit(WelcomeError(message: e.toString()));
    }
  }

  /// Check if welcome should be shown
  Future<bool> shouldShowWelcome() async {
    return await _welcomeService.shouldShowWelcome();
  }

  /// Reset to initial state
  void reset() {
    emit(const WelcomeInitial());
  }
}
