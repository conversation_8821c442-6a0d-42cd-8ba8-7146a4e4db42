import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../domain/models/welcome_model.dart';

class WelcomeWidget extends StatelessWidget {
  final WelcomeModel? config;
  final Function(String)? onButtonPressed;

  const WelcomeWidget({
    super.key,
    this.config,
    this.onButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    // Use default values if config is not provided
    final title = config?.title ?? 'Welcome to the Kerala Family App';
    final subtitle = config?.subtitle ?? 'Connect with your family, preserve your heritage, and celebrate traditions.';
    final backgroundImage = config?.backgroundImagePath ?? 'assets/images/welcome_background.png';

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        color: Color(0xFFFAFAFA),
      ),
      child: Column(
        children: [
          // Background image section
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(backgroundImage),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          // Content section
          Container(
            width: double.infinity,
            color: const Color(0xFFFAFAFA),
            child: Column(
              children: [
                // Title section
                Container(
                  padding: const EdgeInsets.fromLTRB(16, 20, 16, 12),
                  child: Text(
                    title,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w700,
                      fontSize: 28,
                      height: 1.25,
                      color: const Color(0xFF0F1417),
                    ),
                  ),
                ),
                // Subtitle section
                Container(
                  padding: const EdgeInsets.fromLTRB(16, 4, 16, 12),
                  child: Text(
                    subtitle,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 16,
                      height: 1.5,
                      color: const Color(0xFF0F1417),
                    ),
                  ),
                ),
                // Buttons section
                _buildButtons(),
                // Bottom spacing
                const SizedBox(height: 20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtons() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      child: Column(
        children: [
          // Get Started Button
          SizedBox(
            width: double.infinity,
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFDBE8F2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () {
                    if (onButtonPressed != null) {
                      onButtonPressed!('navigate_to_main');
                    } else {
                      debugPrint('Get Started pressed - navigate to main app');
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    child: Center(
                      child: Text(
                        'Get Started',
                        style: GoogleFonts.plusJakartaSans(
                          fontWeight: FontWeight.w700,
                          fontSize: 16,
                          height: 1.5,
                          color: const Color(0xFF0F1417),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Learn More Button
          SizedBox(
            width: double.infinity,
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFEBEDF2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () {
                    if (onButtonPressed != null) {
                      onButtonPressed!('navigate_to_learn_more');
                    } else {
                      debugPrint('Learn More pressed - navigate to learn more page');
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    child: Center(
                      child: Text(
                        'Learn More',
                        style: GoogleFonts.plusJakartaSans(
                          fontWeight: FontWeight.w700,
                          fontSize: 16,
                          height: 1.5,
                          color: const Color(0xFF0F1417),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
