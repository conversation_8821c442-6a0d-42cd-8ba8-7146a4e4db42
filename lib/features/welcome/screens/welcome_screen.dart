import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../widgets/welcome_widget.dart';
import '../domain/models/welcome_model.dart';
import '../../auth/screens/create_account_screen.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: WelcomeWidget(
        config: WelcomeModel(
          title: 'welcome.title'.tr(),
          subtitle: 'welcome.subtitle'.tr(),
          backgroundImagePath: 'assets/images/welcome_background.png',
        ),
        onButtonPressed: (action) {
          _handleButtonAction(context, action);
        },
      ),
    );
  }

  void _handleButtonAction(BuildContext context, String action) {
    switch (action) {
      case 'navigate_to_main':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const CreateAccountScreen(),
          ),
        );
        break;
      case 'navigate_to_learn_more':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Learn More pressed - Navigate to learn more')),
        );
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Unknown action: $action')),
        );
    }
  }
}


