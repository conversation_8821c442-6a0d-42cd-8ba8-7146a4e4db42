import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../widgets/family_widget.dart';
import '../logic/family/family_cubit.dart';
import '../logic/family/family_state.dart';
import '../domain/services/family_service.dart';
import '../domain/repository/supabase_family_repository.dart';
import '../../auth/domain/models/auth_model.dart';
import 'add_family_member_screen.dart';
import '../../../core/network/supabase_client.dart';
import '../../../core/di/injection.dart';

class FamilyScreen extends StatelessWidget {
  final AuthModel? user;

  const FamilyScreen({super.key, this.user});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => FamilyCubit(
        FamilyService(SupabaseFamilyRepository(getIt<SupabaseNetworkClient>())),
      )..loadFamilyTree(user?.id ?? 'default_user'),
      child: FamilyView(user: user),
    );
  }
}

class FamilyView extends StatelessWidget {
  final AuthModel? user;

  const FamilyView({super.key, this.user});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7FAFC),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.family_restroom,
                          color: Color(0xFF0D141C),
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Family Tree',
                        style: GoogleFonts.plusJakartaSans(
                          fontWeight: FontWeight.w700,
                          fontSize: 18,
                          color: const Color(0xFF0D141C),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          _showSearchDialog(context);
                        },
                        icon: const Icon(
                          Icons.search,
                          color: Color(0xFF4A739C),
                          size: 24,
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          _showAddMemberDialog(context);
                        },
                        icon: const Icon(
                          Icons.person_add,
                          color: Color(0xFF0D141C),
                          size: 24,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Family Content
            Expanded(
              child: BlocBuilder<FamilyCubit, FamilyState>(
                builder: (context, state) {
                  if (state is FamilyLoading) {
                    return const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D80F2)),
                      ),
                    );
                  }

                  if (state is FamilyError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Error loading family tree',
                            style: GoogleFonts.plusJakartaSans(
                              fontWeight: FontWeight.w600,
                              fontSize: 18,
                              color: const Color(0xFF0D141C),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.message,
                            style: GoogleFonts.plusJakartaSans(
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              color: const Color(0xFF4A739C),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => context.read<FamilyCubit>().loadFamilyTree(user?.id ?? 'default_user'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0D80F2),
                              foregroundColor: Colors.white,
                            ),
                            child: Text(
                              'Retry',
                              style: GoogleFonts.plusJakartaSans(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  if (state is FamilyLoaded) {
                    return FamilyWidget(
                      familyTree: state.familyTree,
                      members: state.members,
                      membersByGeneration: state.membersByGeneration,
                    );
                  }

                  return const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D80F2)),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    // TODO: Implement search dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Search Family - Coming Soon!',
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        backgroundColor: const Color(0xFF0D80F2),
      ),
    );
  }

  void _showAddMemberDialog(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => FamilyCubit(
            FamilyService(SupabaseFamilyRepository(getIt<SupabaseNetworkClient>())),
          ),
          child: AddFamilyMemberScreen(
            onMemberAdded: () {
              // Refresh the family list when a member is added
              context.read<FamilyCubit>().refreshFamily();
            },
          ),
        ),
      ),
    );
  }
}
