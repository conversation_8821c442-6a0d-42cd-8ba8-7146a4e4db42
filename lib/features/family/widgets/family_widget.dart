import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../domain/models/family_member_model.dart';

class FamilyWidget extends StatelessWidget {
  final FamilyTreeModel familyTree;
  final List<FamilyMemberModel> members;
  final Map<String, List<FamilyMemberModel>> membersByGeneration;

  const FamilyWidget({
    super.key,
    required this.familyTree,
    required this.members,
    required this.membersByGeneration,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Family Tree Visualization
          _buildFamilyTree(),
          
          // Family Members List
          _buildFamilyMembersList(),
          
          // Bottom spacing for navigation
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildFamilyTree() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            familyTree.familyName,
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color: const Color(0xFF0D141C),
            ),
          ),

          const SizedBox(height: 24),

          // Grandparents Level
          if (membersByGeneration['Grandparents']?.isNotEmpty == true)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: membersByGeneration['Grandparents']!
                  .take(2)
                  .map((member) => Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: _buildFamilyMemberFromModel(member),
                      ))
                  .toList(),
            ),
          
          const SizedBox(height: 16),
          
          // Connection Lines
          SizedBox(
            height: 20,
            child: CustomPaint(
              painter: TreeLinePainter(),
              size: const Size(200, 20),
            ),
          ),
          
          // Parents Level
          if (membersByGeneration['Parents']?.isNotEmpty == true)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: membersByGeneration['Parents']!
                  .take(3)
                  .map((member) => _buildFamilyMemberFromModel(member))
                  .toList(),
            ),
          
          const SizedBox(height: 16),
          
          // Connection Lines
          SizedBox(
            height: 20,
            child: CustomPaint(
              painter: TreeLinePainter(),
              size: const Size(200, 20),
            ),
          ),
          
          // Children Level
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildFamilyMember('Arjun', 'Son', Icons.child_care),
              _buildFamilyMember('Ananya', 'Daughter', Icons.child_care),
              _buildFamilyMember('Kiran', 'Cousin', Icons.child_care),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFamilyMember(String name, String relation, IconData icon) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: const Color(0xFFDBE8F2),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: const Color(0xFFE8EDF5),
              width: 2,
            ),
          ),
          child: Icon(
            icon,
            size: 30,
            color: const Color(0xFF4A739C),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          name,
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w600,
            fontSize: 12,
            color: const Color(0xFF0D141C),
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          relation,
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w400,
            fontSize: 10,
            color: const Color(0xFF4A739C),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFamilyMemberFromModel(FamilyMemberModel member) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: const Color(0xFFDBE8F2),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: const Color(0xFFE8EDF5),
              width: 2,
            ),
          ),
          child: member.profileImageUrl != null
              ? ClipOval(
                  child: Image.network(
                    member.profileImageUrl!,
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildMemberIcon(member);
                    },
                  ),
                )
              : _buildMemberIcon(member),
        ),
        const SizedBox(height: 8),
        Text(
          member.name,
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w600,
            fontSize: 12,
            color: const Color(0xFF0D141C),
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          member.relationshipDisplayName,
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w400,
            fontSize: 10,
            color: const Color(0xFF4A739C),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMemberIcon(FamilyMemberModel member) {
    IconData icon;
    switch (member.memberType) {
      case MemberType.male:
        icon = member.relationship == RelationshipType.grandfather
            ? Icons.elderly
            : Icons.person;
        break;
      case MemberType.female:
        icon = member.relationship == RelationshipType.grandmother
            ? Icons.elderly
            : Icons.person;
        break;
      case MemberType.child:
        icon = Icons.child_care;
        break;
    }

    return Icon(
      icon,
      size: 30,
      color: const Color(0xFF4A739C),
    );
  }

  Widget _buildFamilyMembersList() {

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            'Family Members',
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color: const Color(0xFF0D141C),
            ),
          ),
        ),
        
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: members.asMap().entries.map((entry) {
              final index = entry.key;
              final member = entry.value;
              final isLast = index == members.length - 1;
              
              return Column(
                children: [
                  ListTile(
                    leading: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: const Color(0xFFDBE8F2),
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: member.profileImageUrl != null
                          ? ClipOval(
                              child: Image.network(
                                member.profileImageUrl!,
                                width: 50,
                                height: 50,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return _buildMemberIcon(member);
                                },
                              ),
                            )
                          : _buildMemberIcon(member),
                    ),
                    title: Text(
                      member.name,
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: const Color(0xFF0D141C),
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${member.relationshipDisplayName}${member.displayAge.isNotEmpty ? ' • ${member.displayAge}' : ''}',
                          style: GoogleFonts.plusJakartaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                            color: const Color(0xFF4A739C),
                          ),
                        ),
                        if (member.location != null)
                          Text(
                            member.location!,
                            style: GoogleFonts.plusJakartaSans(
                              fontWeight: FontWeight.w400,
                              fontSize: 12,
                              color: const Color(0xFF4A739C),
                            ),
                          ),
                      ],
                    ),
                    trailing: const Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Color(0xFF4A739C),
                    ),
                    onTap: () {
                      _showMemberDetail(member);
                    },
                  ),
                  if (!isLast)
                    const Divider(
                      height: 1,
                      color: Color(0xFFE8EDF5),
                      indent: 82,
                    ),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  void _showMemberDetail(FamilyMemberModel member) {
    // This would typically navigate to a detailed member profile
    debugPrint('Show details for ${member.name}');
  }
}

class TreeLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFE8EDF5)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // Draw connecting lines for family tree
    final path = Path();
    
    // Horizontal line
    path.moveTo(size.width * 0.2, size.height * 0.5);
    path.lineTo(size.width * 0.8, size.height * 0.5);
    
    // Vertical lines
    path.moveTo(size.width * 0.35, size.height * 0.5);
    path.lineTo(size.width * 0.35, size.height);
    
    path.moveTo(size.width * 0.5, size.height * 0.5);
    path.lineTo(size.width * 0.5, size.height);
    
    path.moveTo(size.width * 0.65, size.height * 0.5);
    path.lineTo(size.width * 0.65, size.height);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
