import 'package:equatable/equatable.dart';

enum MemberType {
  male,
  female,
  child,
}

enum RelationshipType {
  grandfather,
  grandmother,
  father,
  mother,
  uncle,
  aunt,
  brother,
  sister,
  cousin,
  son,
  daughter,
  spouse,
  self,
}

class FamilyMemberModel extends Equatable {
  final String id;
  final String name;
  final String? email;
  final String? phoneNumber;
  final String? profileImageUrl;
  final RelationshipType relationship;
  final MemberType memberType;
  final DateTime? dateOfBirth;
  final String? location;
  final String? occupation;
  final String? bio;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? parentId; // For hierarchical relationships
  final List<String> childrenIds; // List of children IDs

  const FamilyMemberModel({
    required this.id,
    required this.name,
    this.email,
    this.phoneNumber,
    this.profileImageUrl,
    required this.relationship,
    required this.memberType,
    this.dateOfBirth,
    this.location,
    this.occupation,
    this.bio,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.parentId,
    this.childrenIds = const [],
  });

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phoneNumber,
        profileImageUrl,
        relationship,
        memberType,
        dateOfBirth,
        location,
        occupation,
        bio,
        isActive,
        createdAt,
        updatedAt,
        parentId,
        childrenIds,
      ];

  FamilyMemberModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phoneNumber,
    String? profileImageUrl,
    RelationshipType? relationship,
    MemberType? memberType,
    DateTime? dateOfBirth,
    String? location,
    String? occupation,
    String? bio,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? parentId,
    List<String>? childrenIds,
  }) {
    return FamilyMemberModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      relationship: relationship ?? this.relationship,
      memberType: memberType ?? this.memberType,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      location: location ?? this.location,
      occupation: occupation ?? this.occupation,
      bio: bio ?? this.bio,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      parentId: parentId ?? this.parentId,
      childrenIds: childrenIds ?? this.childrenIds,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone_number': phoneNumber,
      'profile_image_url': profileImageUrl,
      'relationship': relationship.name,
      'member_type': memberType.name,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'location': location,
      'occupation': occupation,
      'bio': bio,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'parent_id': parentId,
      'children_ids': childrenIds,
    };
  }

  factory FamilyMemberModel.fromJson(Map<String, dynamic> json) {
    return FamilyMemberModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String?,
      phoneNumber: json['phone_number'] as String?,
      profileImageUrl: json['profile_image_url'] as String?,
      relationship: RelationshipType.values.firstWhere(
        (e) => e.name == json['relationship'],
        orElse: () => RelationshipType.self,
      ),
      memberType: MemberType.values.firstWhere(
        (e) => e.name == json['member_type'],
        orElse: () => MemberType.male,
      ),
      dateOfBirth: json['date_of_birth'] != null
          ? DateTime.parse(json['date_of_birth'] as String)
          : null,
      location: json['location'] as String?,
      occupation: json['occupation'] as String?,
      bio: json['bio'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      parentId: json['parent_id'] as String?,
      childrenIds: (json['children_ids'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
    );
  }

  String get displayAge {
    if (dateOfBirth == null) return '';
    final age = DateTime.now().difference(dateOfBirth!).inDays ~/ 365;
    return '$age years';
  }

  String get relationshipDisplayName {
    switch (relationship) {
      case RelationshipType.grandfather:
        return 'Grandfather';
      case RelationshipType.grandmother:
        return 'Grandmother';
      case RelationshipType.father:
        return 'Father';
      case RelationshipType.mother:
        return 'Mother';
      case RelationshipType.uncle:
        return 'Uncle';
      case RelationshipType.aunt:
        return 'Aunt';
      case RelationshipType.brother:
        return 'Brother';
      case RelationshipType.sister:
        return 'Sister';
      case RelationshipType.cousin:
        return 'Cousin';
      case RelationshipType.son:
        return 'Son';
      case RelationshipType.daughter:
        return 'Daughter';
      case RelationshipType.spouse:
        return 'Spouse';
      case RelationshipType.self:
        return 'Self';
    }
  }
}

class FamilyTreeModel extends Equatable {
  final String id;
  final String familyName;
  final String ownerId;
  final List<FamilyMemberModel> members;
  final DateTime createdAt;
  final DateTime updatedAt;

  const FamilyTreeModel({
    required this.id,
    required this.familyName,
    required this.ownerId,
    required this.members,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        familyName,
        ownerId,
        members,
        createdAt,
        updatedAt,
      ];

  FamilyTreeModel copyWith({
    String? id,
    String? familyName,
    String? ownerId,
    List<FamilyMemberModel>? members,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FamilyTreeModel(
      id: id ?? this.id,
      familyName: familyName ?? this.familyName,
      ownerId: ownerId ?? this.ownerId,
      members: members ?? this.members,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
