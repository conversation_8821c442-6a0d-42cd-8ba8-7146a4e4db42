import '../models/family_member_model.dart';

/// Abstract repository interface for family operations
abstract class FamilyRepository {
  /// Get family tree for the current user
  Future<FamilyTreeModel?> getFamilyTree(String userId);

  /// Create a new family tree
  Future<FamilyTreeModel> createFamilyTree(String userId, String familyName);

  /// Add a family member
  Future<FamilyMemberModel> addFamilyMember(FamilyMemberModel member);

  /// Update a family member
  Future<FamilyMemberModel> updateFamilyMember(FamilyMemberModel member);

  /// Delete a family member
  Future<void> deleteFamilyMember(String memberId);

  /// Get all family members for a family tree
  Future<List<FamilyMemberModel>> getFamilyMembers(String familyTreeId);

  /// Get a specific family member by ID
  Future<FamilyMemberModel?> getFamilyMember(String memberId);

  /// Search family members by name
  Future<List<FamilyMemberModel>> searchFamilyMembers(String familyTreeId, String query);

  /// Upload family member profile image
  Future<String> uploadMemberImage(String memberId, String imagePath);

  /// Delete family member profile image
  Future<void> deleteMemberImage(String memberId);
}

class FamilyResponse {
  final bool success;
  final String? message;
  final FamilyMemberModel? member;
  final FamilyTreeModel? familyTree;
  final List<FamilyMemberModel>? members;

  const FamilyResponse({
    required this.success,
    this.message,
    this.member,
    this.familyTree,
    this.members,
  });
}
