import '../models/family_member_model.dart';
import 'family_repository.dart';

class MockFamilyRepository implements FamilyRepository {
  static final Map<String, FamilyTreeModel> _familyTrees = {};
  static final Map<String, List<FamilyMemberModel>> _familyMembers = {};
  static int _memberIdCounter = 1;

  @override
  Future<FamilyTreeModel?> getFamilyTree(String userId) async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
    
    // Return existing family tree or null
    return _familyTrees[userId];
  }

  @override
  Future<FamilyTreeModel> createFamilyTree(String userId, String familyName) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final now = DateTime.now();
    final familyTree = FamilyTreeModel(
      id: 'family_tree_$userId',
      familyName: familyName,
      ownerId: userId,
      members: [],
      createdAt: now,
      updatedAt: now,
    );

    _familyTrees[userId] = familyTree;
    _familyMembers[familyTree.id] = _getDefaultFamilyMembers(familyTree.id);

    return familyTree;
  }

  @override
  Future<FamilyMemberModel> addFamilyMember(FamilyMemberModel member) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final now = DateTime.now();
    final newMember = member.copyWith(
      id: 'member_${_memberIdCounter++}',
      createdAt: now,
      updatedAt: now,
    );

    // Find the family tree this member belongs to
    String? familyTreeId;
    for (final entry in _familyMembers.entries) {
      familyTreeId = entry.key;
      break; // For simplicity, add to first family tree
    }

    if (familyTreeId != null) {
      _familyMembers[familyTreeId] = [
        ..._familyMembers[familyTreeId] ?? [],
        newMember,
      ];
    }

    return newMember;
  }

  @override
  Future<FamilyMemberModel> updateFamilyMember(FamilyMemberModel member) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final updatedMember = member.copyWith(updatedAt: DateTime.now());

    // Find and update the member
    for (final familyTreeId in _familyMembers.keys) {
      final members = _familyMembers[familyTreeId] ?? [];
      final memberIndex = members.indexWhere((m) => m.id == member.id);
      
      if (memberIndex != -1) {
        final updatedMembers = [...members];
        updatedMembers[memberIndex] = updatedMember;
        _familyMembers[familyTreeId] = updatedMembers;
        break;
      }
    }

    return updatedMember;
  }

  @override
  Future<void> deleteFamilyMember(String memberId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    // Find and remove the member
    for (final familyTreeId in _familyMembers.keys) {
      final members = _familyMembers[familyTreeId] ?? [];
      _familyMembers[familyTreeId] = members.where((m) => m.id != memberId).toList();
    }
  }

  @override
  Future<List<FamilyMemberModel>> getFamilyMembers(String familyTreeId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return _familyMembers[familyTreeId] ?? [];
  }

  @override
  Future<FamilyMemberModel?> getFamilyMember(String memberId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    for (final members in _familyMembers.values) {
      final member = members.firstWhere(
        (m) => m.id == memberId,
        orElse: () => throw StateError('Member not found'),
      );
      if (member.id == memberId) return member;
    }
    return null;
  }

  @override
  Future<List<FamilyMemberModel>> searchFamilyMembers(String familyTreeId, String query) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final members = _familyMembers[familyTreeId] ?? [];
    final lowercaseQuery = query.toLowerCase();

    return members.where((member) {
      return member.name.toLowerCase().contains(lowercaseQuery) ||
          member.relationshipDisplayName.toLowerCase().contains(lowercaseQuery) ||
          (member.location?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  @override
  Future<String> uploadMemberImage(String memberId, String imagePath) async {
    await Future.delayed(const Duration(milliseconds: 1000));
    
    // Simulate image upload and return a mock URL
    return 'https://example.com/images/member_$memberId.jpg';
  }

  @override
  Future<void> deleteMemberImage(String memberId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // Simulate image deletion
  }

  List<FamilyMemberModel> _getDefaultFamilyMembers(String familyTreeId) {
    final now = DateTime.now();
    
    return [
      FamilyMemberModel(
        id: 'member_1',
        name: 'Raman Nair',
        relationship: RelationshipType.grandfather,
        memberType: MemberType.male,
        dateOfBirth: DateTime(1945, 5, 15),
        location: 'Kochi, Kerala',
        occupation: 'Retired Teacher',
        createdAt: now,
        updatedAt: now,
      ),
      FamilyMemberModel(
        id: 'member_2',
        name: 'Kamala Nair',
        relationship: RelationshipType.grandmother,
        memberType: MemberType.female,
        dateOfBirth: DateTime(1948, 8, 22),
        location: 'Kochi, Kerala',
        occupation: 'Homemaker',
        createdAt: now,
        updatedAt: now,
      ),
      FamilyMemberModel(
        id: 'member_3',
        name: 'Suresh Nair',
        relationship: RelationshipType.father,
        memberType: MemberType.male,
        dateOfBirth: DateTime(1971, 3, 10),
        location: 'Kochi, Kerala',
        occupation: 'Engineer',
        email: '<EMAIL>',
        phoneNumber: '+91 9876543210',
        createdAt: now,
        updatedAt: now,
      ),
      FamilyMemberModel(
        id: 'member_4',
        name: 'Priya Nair',
        relationship: RelationshipType.mother,
        memberType: MemberType.female,
        dateOfBirth: DateTime(1975, 11, 28),
        location: 'Kochi, Kerala',
        occupation: 'Doctor',
        email: '<EMAIL>',
        phoneNumber: '+91 9876543211',
        createdAt: now,
        updatedAt: now,
      ),
      FamilyMemberModel(
        id: 'member_5',
        name: 'Rajesh Nair',
        relationship: RelationshipType.uncle,
        memberType: MemberType.male,
        dateOfBirth: DateTime(1968, 7, 5),
        location: 'Thiruvananthapuram, Kerala',
        occupation: 'Business Owner',
        email: '<EMAIL>',
        phoneNumber: '+91 9876543212',
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}
