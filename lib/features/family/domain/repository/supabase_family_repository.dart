import 'package:injectable/injectable.dart';
import '../models/family_member_model.dart';
import 'family_repository.dart';
import '../../../../core/network/supabase_client.dart';

@Injectable(as: FamilyRepository)
class SupabaseFamilyRepository implements FamilyRepository {
  final SupabaseNetworkClient _networkClient;

  SupabaseFamilyRepository(this._networkClient);

  @override
  Future<FamilyTreeModel?> getFamilyTree(String userId) async {
    try {
      // For now, return a default family tree
      // In a real app, you'd query for user's family tree
      final now = DateTime.now();
      return FamilyTreeModel(
        id: 'default_family_tree',
        familyName: 'Nair Family Tree',
        ownerId: userId,
        members: [],
        createdAt: now,
        updatedAt: now,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<FamilyTreeModel> createFamilyTree(String userId, String familyName) async {
    final now = DateTime.now();
    return FamilyTreeModel(
      id: 'default_family_tree',
      familyName: familyName,
      ownerId: userId,
      members: [],
      createdAt: now,
      updatedAt: now,
    );
  }

  @override
  Future<List<FamilyMemberModel>> getFamilyMembers(String familyTreeId) async {
    try {
      final response = await _networkClient.client
          .from('family_members')
          .select('*')
          .eq('is_alive', true);

      final List<dynamic> data = response as List<dynamic>;
      
      return data.map((json) => _mapToFamilyMemberModel(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch family members: $e');
    }
  }

  @override
  Future<FamilyMemberModel> addFamilyMember(FamilyMemberModel member) async {
    try {
      final memberData = {
        'name': member.name,
        'relationship': member.relationship.name,
        'date_of_birth': member.dateOfBirth?.toIso8601String().split('T')[0],
        'phone_number': member.phoneNumber,
        'email': member.email,
        'current_location': member.location,
        'occupation': member.occupation,
        'is_alive': member.isActive,
        'photo_url': member.profileImageUrl,
      };

      final response = await _networkClient.client
          .from('family_members')
          .insert(memberData)
          .select()
          .single();

      return _mapToFamilyMemberModel(response);
    } catch (e) {
      throw Exception('Failed to add family member: $e');
    }
  }

  @override
  Future<FamilyMemberModel> updateFamilyMember(FamilyMemberModel member) async {
    try {
      final memberData = {
        'name': member.name,
        'relationship': member.relationship.name,
        'date_of_birth': member.dateOfBirth?.toIso8601String().split('T')[0],
        'phone_number': member.phoneNumber,
        'email': member.email,
        'current_location': member.location,
        'occupation': member.occupation,
        'is_alive': member.isActive,
        'photo_url': member.profileImageUrl,
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await _networkClient.client
          .from('family_members')
          .update(memberData)
          .eq('id', member.id)
          .select()
          .single();

      return _mapToFamilyMemberModel(response);
    } catch (e) {
      throw Exception('Failed to update family member: $e');
    }
  }

  @override
  Future<void> deleteFamilyMember(String memberId) async {
    try {
      await _networkClient.client
          .from('family_members')
          .delete()
          .eq('id', memberId);
    } catch (e) {
      throw Exception('Failed to delete family member: $e');
    }
  }

  @override
  Future<FamilyMemberModel?> getFamilyMember(String memberId) async {
    try {
      final response = await _networkClient.client
          .from('family_members')
          .select('*')
          .eq('id', memberId)
          .single();

      return _mapToFamilyMemberModel(response);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<FamilyMemberModel>> searchFamilyMembers(String familyTreeId, String query) async {
    try {
      final response = await _networkClient.client
          .from('family_members')
          .select('*')
          .or('name.ilike.%$query%,relationship.ilike.%$query%,current_location.ilike.%$query%')
          .eq('is_alive', true);

      final List<dynamic> data = response as List<dynamic>;
      
      return data.map((json) => _mapToFamilyMemberModel(json)).toList();
    } catch (e) {
      throw Exception('Failed to search family members: $e');
    }
  }

  @override
  Future<String> uploadMemberImage(String memberId, String imagePath) async {
    try {
      // For now, return a placeholder URL
      // In a real implementation, you'd upload to Supabase Storage
      return 'https://via.placeholder.com/150';
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }

  @override
  Future<void> deleteMemberImage(String memberId) async {
    try {
      await _networkClient.client
          .from('family_members')
          .update({'photo_url': null})
          .eq('id', memberId);
    } catch (e) {
      throw Exception('Failed to delete image: $e');
    }
  }

  FamilyMemberModel _mapToFamilyMemberModel(Map<String, dynamic> json) {
    return FamilyMemberModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String?,
      phoneNumber: json['phone_number'] as String?,
      profileImageUrl: json['photo_url'] as String?,
      relationship: _parseRelationshipType(json['relationship'] as String?),
      memberType: _parseMemberType(json['relationship'] as String?),
      dateOfBirth: json['date_of_birth'] != null 
          ? DateTime.parse(json['date_of_birth'] as String)
          : null,
      location: json['current_location'] as String?,
      occupation: json['occupation'] as String?,
      bio: json['additional_info']?['bio'] as String?,
      isActive: json['is_alive'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  RelationshipType _parseRelationshipType(String? relationship) {
    if (relationship == null) return RelationshipType.self;
    
    switch (relationship.toLowerCase()) {
      case 'grandfather':
        return RelationshipType.grandfather;
      case 'grandmother':
        return RelationshipType.grandmother;
      case 'father':
        return RelationshipType.father;
      case 'mother':
        return RelationshipType.mother;
      case 'uncle':
        return RelationshipType.uncle;
      case 'aunt':
        return RelationshipType.aunt;
      case 'brother':
        return RelationshipType.brother;
      case 'sister':
        return RelationshipType.sister;
      case 'cousin':
        return RelationshipType.cousin;
      case 'son':
        return RelationshipType.son;
      case 'daughter':
        return RelationshipType.daughter;
      case 'spouse':
        return RelationshipType.spouse;
      default:
        return RelationshipType.self;
    }
  }

  MemberType _parseMemberType(String? relationship) {
    if (relationship == null) return MemberType.male;
    
    switch (relationship.toLowerCase()) {
      case 'grandmother':
      case 'mother':
      case 'aunt':
      case 'sister':
        return MemberType.female;
      case 'son':
      case 'daughter':
        return MemberType.child;
      default:
        return MemberType.male;
    }
  }
}
