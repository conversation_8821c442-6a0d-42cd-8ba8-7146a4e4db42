import '../models/family_member_model.dart';
import '../repository/family_repository.dart';

class FamilyService {
  final FamilyRepository _repository;

  FamilyService(this._repository);

  /// Get or create family tree for user
  Future<FamilyTreeModel> getOrCreateFamilyTree(String userId, String? familyName) async {
    try {
      final existingTree = await _repository.getFamilyTree(userId);
      if (existingTree != null) {
        return existingTree;
      }

      // Create new family tree with default name
      final defaultFamilyName = familyName ?? 'Family Tree';
      return await _repository.createFamilyTree(userId, defaultFamilyName);
    } catch (e) {
      throw Exception('Failed to get or create family tree: $e');
    }
  }

  /// Add a new family m ember with validation
  Future<FamilyMemberModel> addFamilyMember({
    required String name,
    required RelationshipType relationship,
    required MemberType memberType,
    String? email,
    String? phoneNumber,
    DateTime? dateOfBirth,
    String? location,
    String? occupation,
    String? bio,
    String? parentId,
  }) async {
    try {
      // Validate required fields
      if (name.trim().isEmpty) {
        throw Exception('Name is required');
      }

      final now = DateTime.now();
      final member = FamilyMemberModel(
        id: '', // Will be generated by repository
        name: name.trim(),
        email: email?.trim(),
        phoneNumber: phoneNumber?.trim(),
        relationship: relationship,
        memberType: memberType,
        dateOfBirth: dateOfBirth,
        location: location?.trim(),
        occupation: occupation?.trim(),
        bio: bio?.trim(),
        createdAt: now,
        updatedAt: now,
        parentId: parentId,
      );

      return await _repository.addFamilyMember(member);
    } catch (e) {
      throw Exception('Failed to add family member: $e');
    }
  }

  /// Update family member
  Future<FamilyMemberModel> updateFamilyMember(FamilyMemberModel member) async {
    try {
      // Validate required fields
      if (member.name.trim().isEmpty) {
        throw Exception('Name is required');
      }

      final updatedMember = member.copyWith(
        updatedAt: DateTime.now(),
      );

      return await _repository.updateFamilyMember(updatedMember);
    } catch (e) {
      throw Exception('Failed to update family member: $e');
    }
  }

  /// Delete family member with confirmation
  Future<void> deleteFamilyMember(String memberId) async {
    try {
      await _repository.deleteFamilyMember(memberId);
    } catch (e) {
      throw Exception('Failed to delete family member: $e');
    }
  }

  /// Get all family members
  Future<List<FamilyMemberModel>> getFamilyMembers(String familyTreeId) async {
    try {
      return await _repository.getFamilyMembers(familyTreeId);
    } catch (e) {
      throw Exception('Failed to get family members: $e');
    }
  }

  /// Search family members
  Future<List<FamilyMemberModel>> searchFamilyMembers(String familyTreeId, String query) async {
    try {
      if (query.trim().isEmpty) {
        return await getFamilyMembers(familyTreeId);
      }
      return await _repository.searchFamilyMembers(familyTreeId, query.trim());
    } catch (e) {
      throw Exception('Failed to search family members: $e');
    }
  }

  /// Upload member profile image
  Future<String> uploadMemberImage(String memberId, String imagePath) async {
    try {
      return await _repository.uploadMemberImage(memberId, imagePath);
    } catch (e) {
      throw Exception('Failed to upload member image: $e');
    }
  }

  /// Get default avatar based on member type
  String getDefaultAvatarPath(MemberType memberType) {
    switch (memberType) {
      case MemberType.male:
        return 'assets/images/avatar_male.png';
      case MemberType.female:
        return 'assets/images/avatar_female.png';
      case MemberType.child:
        return 'assets/images/avatar_child.png';
    }
  }

  /// Validate email format
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validate phone number format
  bool isValidPhoneNumber(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    return RegExp(r'^(\+91|91)?[6-9]\d{9}$').hasMatch(cleanPhone);
  }

  /// Get family members organized by generation
  Map<String, List<FamilyMemberModel>> organizeByGeneration(List<FamilyMemberModel> members) {
    final Map<String, List<FamilyMemberModel>> generations = {
      'Grandparents': [],
      'Parents': [],
      'Siblings': [],
      'Children': [],
      'Others': [],
    };

    for (final member in members) {
      switch (member.relationship) {
        case RelationshipType.grandfather:
        case RelationshipType.grandmother:
          generations['Grandparents']!.add(member);
          break;
        case RelationshipType.father:
        case RelationshipType.mother:
          generations['Parents']!.add(member);
          break;
        case RelationshipType.brother:
        case RelationshipType.sister:
        case RelationshipType.cousin:
          generations['Siblings']!.add(member);
          break;
        case RelationshipType.son:
        case RelationshipType.daughter:
          generations['Children']!.add(member);
          break;
        default:
          generations['Others']!.add(member);
          break;
      }
    }

    return generations;
  }
}
