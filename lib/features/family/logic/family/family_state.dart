import 'package:equatable/equatable.dart';
import '../../domain/models/family_member_model.dart';

abstract class FamilyState extends Equatable {
  const FamilyState();

  @override
  List<Object?> get props => [];
}

class FamilyInitial extends FamilyState {
  const FamilyInitial();
}

class FamilyLoading extends FamilyState {
  const FamilyLoading();
}

class FamilyLoaded extends FamilyState {
  final FamilyTreeModel familyTree;
  final List<FamilyMemberModel> members;
  final Map<String, List<FamilyMemberModel>> membersByGeneration;

  const FamilyLoaded({
    required this.familyTree,
    required this.members,
    required this.membersByGeneration,
  });

  @override
  List<Object?> get props => [familyTree, members, membersByGeneration];

  FamilyLoaded copyWith({
    FamilyTreeModel? familyTree,
    List<FamilyMemberModel>? members,
    Map<String, List<FamilyMemberModel>>? membersByGeneration,
  }) {
    return FamilyLoaded(
      familyTree: familyTree ?? this.familyTree,
      members: members ?? this.members,
      membersByGeneration: membersByGeneration ?? this.membersByGeneration,
    );
  }
}

class FamilyError extends FamilyState {
  final String message;

  const FamilyError({required this.message});

  @override
  List<Object?> get props => [message];
}

class FamilyMemberAdding extends FamilyState {
  const FamilyMemberAdding();
}

class FamilyMemberAdded extends FamilyState {
  final FamilyMemberModel member;
  final String? message;

  const FamilyMemberAdded({
    required this.member,
    this.message,
  });

  @override
  List<Object?> get props => [member, message];
}

class FamilyMemberUpdating extends FamilyState {
  const FamilyMemberUpdating();
}

class FamilyMemberUpdated extends FamilyState {
  final FamilyMemberModel member;
  final String? message;

  const FamilyMemberUpdated({
    required this.member,
    this.message,
  });

  @override
  List<Object?> get props => [member, message];
}

class FamilyMemberDeleting extends FamilyState {
  const FamilyMemberDeleting();
}

class FamilyMemberDeleted extends FamilyState {
  final String memberId;
  final String? message;

  const FamilyMemberDeleted({
    required this.memberId,
    this.message,
  });

  @override
  List<Object?> get props => [memberId, message];
}

class FamilySearching extends FamilyState {
  const FamilySearching();
}

class FamilySearchResults extends FamilyState {
  final List<FamilyMemberModel> results;
  final String query;

  const FamilySearchResults({
    required this.results,
    required this.query,
  });

  @override
  List<Object?> get props => [results, query];
}

class FamilyImageUploading extends FamilyState {
  const FamilyImageUploading();
}

class FamilyImageUploaded extends FamilyState {
  final String imageUrl;
  final String memberId;

  const FamilyImageUploaded({
    required this.imageUrl,
    required this.memberId,
  });

  @override
  List<Object?> get props => [imageUrl, memberId];
}
