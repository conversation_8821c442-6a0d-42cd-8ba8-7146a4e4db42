import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/services/family_service.dart';
import '../../domain/models/family_member_model.dart';
import 'family_state.dart';

class FamilyCubit extends Cubit<FamilyState> {
  final FamilyService _familyService;

  FamilyCubit(this._familyService) : super(const FamilyInitial());

  /// Load family tree and members
  Future<void> loadFamilyTree(String userId, {String? familyName}) async {
    try {
      emit(const FamilyLoading());

      final familyTree = await _familyService.getOrCreateFamilyTree(userId, familyName);
      final members = await _familyService.getFamilyMembers(familyTree.id);
      final membersByGeneration = _familyService.organizeByGeneration(members);

      emit(FamilyLoaded(
        familyTree: familyTree,
        members: members,
        membersByGeneration: membersByGeneration,
      ));
    } catch (e) {
      emit(FamilyError(message: e.toString()));
    }
  }

  /// Add a new family member
  Future<void> addFamilyMember({
    required String name,
    required RelationshipType relationship,
    required MemberType memberType,
    String? email,
    String? phoneNumber,
    DateTime? dateOfBirth,
    String? location,
    String? occupation,
    String? bio,
    String? parentId,
  }) async {
    try {
      emit(const FamilyMemberAdding());

      final member = await _familyService.addFamilyMember(
        name: name,
        relationship: relationship,
        memberType: memberType,
        email: email,
        phoneNumber: phoneNumber,
        dateOfBirth: dateOfBirth,
        location: location,
        occupation: occupation,
        bio: bio,
        parentId: parentId,
      );

      emit(FamilyMemberAdded(
        member: member,
        message: 'Family member added successfully',
      ));

      // Reload family tree to show updated data
      final currentState = state;
      if (currentState is FamilyLoaded) {
        await loadFamilyTree(currentState.familyTree.ownerId);
      }
    } catch (e) {
      emit(FamilyError(message: e.toString()));
    }
  }

  /// Update family member
  Future<void> updateFamilyMember(FamilyMemberModel member) async {
    try {
      emit(const FamilyMemberUpdating());

      final updatedMember = await _familyService.updateFamilyMember(member);

      emit(FamilyMemberUpdated(
        member: updatedMember,
        message: 'Family member updated successfully',
      ));

      // Reload family tree to show updated data
      final currentState = state;
      if (currentState is FamilyLoaded) {
        await loadFamilyTree(currentState.familyTree.ownerId);
      }
    } catch (e) {
      emit(FamilyError(message: e.toString()));
    }
  }

  /// Delete family member
  Future<void> deleteFamilyMember(String memberId) async {
    try {
      emit(const FamilyMemberDeleting());

      await _familyService.deleteFamilyMember(memberId);

      emit(FamilyMemberDeleted(
        memberId: memberId,
        message: 'Family member deleted successfully',
      ));

      // Reload family tree to show updated data
      final currentState = state;
      if (currentState is FamilyLoaded) {
        await loadFamilyTree(currentState.familyTree.ownerId);
      }
    } catch (e) {
      emit(FamilyError(message: e.toString()));
    }
  }

  /// Search family members
  Future<void> searchFamilyMembers(String query) async {
    try {
      final currentState = state;
      if (currentState is! FamilyLoaded) return;

      emit(const FamilySearching());

      final results = await _familyService.searchFamilyMembers(
        currentState.familyTree.id,
        query,
      );

      emit(FamilySearchResults(
        results: results,
        query: query,
      ));
    } catch (e) {
      emit(FamilyError(message: e.toString()));
    }
  }

  /// Clear search results and return to main view
  void clearSearch() {
    final currentState = state;
    if (currentState is FamilyLoaded) {
      emit(currentState);
    }
  }

  /// Upload member profile image
  Future<void> uploadMemberImage(String memberId, String imagePath) async {
    try {
      emit(const FamilyImageUploading());

      final imageUrl = await _familyService.uploadMemberImage(memberId, imagePath);

      emit(FamilyImageUploaded(
        imageUrl: imageUrl,
        memberId: memberId,
      ));

      // Reload family tree to show updated image
      final currentState = state;
      if (currentState is FamilyLoaded) {
        await loadFamilyTree(currentState.familyTree.ownerId);
      }
    } catch (e) {
      emit(FamilyError(message: e.toString()));
    }
  }

  /// Refresh family data
  Future<void> refreshFamily() async {
    final currentState = state;
    if (currentState is FamilyLoaded) {
      await loadFamilyTree(currentState.familyTree.ownerId);
    }
  }

  /// Reset to initial state
  void reset() {
    emit(const FamilyInitial());
  }

  /// Clear error state
  void clearError() {
    if (state is FamilyError) {
      emit(const FamilyInitial());
    }
  }
}
