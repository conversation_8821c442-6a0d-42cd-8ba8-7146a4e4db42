import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:google_fonts/google_fonts.dart';
import '../logic/dashboard/dashboard_cubit.dart';
import '../logic/dashboard/dashboard_state.dart';
import '../widgets/dashboard_widget.dart';
import '../domain/services/dashboard_service.dart';
import '../domain/repository/mock_dashboard_repository.dart';
import '../../profile/screens/profile_screen.dart';
import '../../family/screens/family_screen.dart';
import '../../events/screens/events_screen.dart';
import '../../recipes/screens/recipes_screen.dart';
import '../../memories/screens/memories_screen.dart';
import '../../auth/domain/models/auth_model.dart';

class DashboardScreen extends StatelessWidget {
  final AuthModel? user;

  const DashboardScreen({super.key, this.user});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DashboardCubit(
        DashboardService(MockDashboardRepository()),
      )..loadDashboard(
        userId: user?.id,
        userName: user?.name ?? 'Family Member',
        userEmail: user?.email,
        userPhoneNumber: user?.phoneNumber,
      ),
      child: DashboardView(user: user),
    );
  }
}

class DashboardView extends StatelessWidget {
  final AuthModel? user;

  const DashboardView({super.key, this.user});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardCubit, DashboardState>(
      builder: (context, state) {
        if (state is DashboardLoading) {
          return const Scaffold(
            backgroundColor: Color(0xFFF7FAFC),
            body: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D80F2)),
              ),
            ),
          );
        }
        
        if (state is DashboardError) {
          return Scaffold(
            backgroundColor: Color(0xFFF7FAFC),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error loading dashboard',
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                      color: const Color(0xFF0D141C),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      color: const Color(0xFF4A739C),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => context.read<DashboardCubit>().loadDashboard(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0D80F2),
                      foregroundColor: Colors.white,
                    ),
                    child: Text(
                      'Retry',
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
        
        if (state is DashboardLoaded || state is DashboardRefreshing || state is DashboardTabChanged) {
          final dashboardData = state is DashboardLoaded 
              ? state.dashboard 
              : state is DashboardRefreshing 
                  ? state.currentDashboard
                  : (state as DashboardTabChanged).dashboard;
          
          final bottomNavItems = state is DashboardLoaded 
              ? state.bottomNavItems 
              : state is DashboardRefreshing 
                  ? state.bottomNavItems
                  : (state as DashboardTabChanged).bottomNavItems;
          
          final currentTabIndex = state is DashboardLoaded 
              ? state.currentTabIndex 
              : state is DashboardRefreshing 
                  ? state.currentTabIndex
                  : (state as DashboardTabChanged).newTabIndex;
          
          return Scaffold(
            backgroundColor: const Color(0xFFF7FAFC),
            body: IndexedStack(
              index: currentTabIndex,
              children: [
                // Home Tab
                DashboardWidget(
                  dashboard: dashboardData,
                  isRefreshing: state is DashboardRefreshing,
                  onRefresh: () => context.read<DashboardCubit>().refreshDashboard(),
                  onQuickActionTap: (actionId) => context.read<DashboardCubit>().onQuickActionTap(actionId),
                  onRecentActivityTap: (activityId) => context.read<DashboardCubit>().onRecentActivityTap(activityId),
                ),
                // Family Tab
                FamilyScreen(user: user),
                // Events Tab
                const EventsScreen(),
                // Recipes Tab
                const RecipesScreen(),
                // Memories Tab
                const MemoriesScreen(),
                // Profile Tab
                const ProfileScreen(),
              ],
            ),
            bottomNavigationBar: _buildBottomNavigationBar(
              context,
              bottomNavItems,
              currentTabIndex,
            ),
          );
        }
        
        // Default loading state
        return const Scaffold(
          backgroundColor: Color(0xFFF7FAFC),
          body: Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D80F2)),
            ),
          ),
        );
      },
    );
  }



  Widget _buildBottomNavigationBar(
    BuildContext context,
    List<dynamic> bottomNavItems,
    int currentIndex,
  ) {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFFF7FAFC),
        border: Border(
          top: BorderSide(
            color: Color(0xFFE8EDF5),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Container(
          height: 95,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(6, (index) {
              final isSelected = index == currentIndex;
              return GestureDetector(
                onTap: () => context.read<DashboardCubit>().changeTab(index),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(27),
                    color: isSelected ? Colors.transparent : Colors.transparent,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getIconForIndex(index),
                        size: 24,
                        color: isSelected ? const Color(0xFF0D141C) : const Color(0xFF4A739C),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getLabelForIndex(index),
                        style: GoogleFonts.plusJakartaSans(
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                          color: isSelected ? const Color(0xFF0D141C) : const Color(0xFF4A739C),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }

  IconData _getIconForIndex(int index) {
    switch (index) {
      case 0:
        return Icons.home;
      case 1:
        return Icons.family_restroom;
      case 2:
        return Icons.event;
      case 3:
        return Icons.restaurant_menu;
      case 4:
        return Icons.photo_library;
      case 5:
        return Icons.person;
      default:
        return Icons.home;
    }
  }

  String _getLabelForIndex(int index) {
    switch (index) {
      case 0:
        return 'navigation.home'.tr();
      case 1:
        return 'navigation.family'.tr();
      case 2:
        return 'navigation.events'.tr();
      case 3:
        return 'Today\'s\n Special';
      case 4:
        return 'navigation.memories'.tr();
      case 5:
        return 'navigation.profile'.tr();
      default:
        return 'navigation.home'.tr();
    }
  }
}
