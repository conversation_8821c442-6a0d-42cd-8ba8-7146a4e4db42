import 'package:equatable/equatable.dart';

class DashboardModel extends Equatable {
  final String userId;
  final String userName;
  final String? userEmail;
  final String? userPhoneNumber;
  final String? userProfileImageUrl;
  final List<QuickAction> quickActions;
  final List<RecentActivity> recentActivities;
  final int currentTabIndex;

  const DashboardModel({
    required this.userId,
    required this.userName,
    this.userEmail,
    this.userPhoneNumber,
    this.userProfileImageUrl,
    required this.quickActions,
    required this.recentActivities,
    this.currentTabIndex = 0,
  });

  @override
  List<Object?> get props => [
        userId,
        userName,
        userEmail,
        userPhoneNumber,
        userProfileImageUrl,
        quickActions,
        recentActivities,
        currentTabIndex,
      ];

  DashboardModel copyWith({
    String? userId,
    String? userName,
    String? userEmail,
    String? userPhoneNumber,
    String? userProfileImageUrl,
    List<QuickAction>? quickActions,
    List<RecentActivity>? recentActivities,
    int? currentTabIndex,
  }) {
    return DashboardModel(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      userPhoneNumber: userPhoneNumber ?? this.userPhoneNumber,
      userProfileImageUrl: userProfileImageUrl ?? this.userProfileImageUrl,
      quickActions: quickActions ?? this.quickActions,
      recentActivities: recentActivities ?? this.recentActivities,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
    );
  }

  String get userInitials {
    if (userName.isEmpty) return 'U';
    final names = userName.split(' ');
    if (names.length == 1) {
      return names[0].substring(0, 1).toUpperCase();
    }
    return '${names[0].substring(0, 1)}${names[1].substring(0, 1)}'.toUpperCase();
  }
}

class QuickAction extends Equatable {
  final String id;
  final String title;
  final String icon;
  final String route;
  final bool isActive;

  const QuickAction({
    required this.id,
    required this.title,
    required this.icon,
    required this.route,
    this.isActive = true,
  });

  @override
  List<Object?> get props => [id, title, icon, route, isActive];
}

class RecentActivity extends Equatable {
  final String id;
  final String title;
  final String description;
  final DateTime timestamp;
  final String type;
  final String? imageUrl;

  const RecentActivity({
    required this.id,
    required this.title,
    required this.description,
    required this.timestamp,
    required this.type,
    this.imageUrl,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        timestamp,
        type,
        imageUrl,
      ];
}

class BottomNavItem extends Equatable {
  final String id;
  final String label;
  final String icon;
  final String route;
  final bool isSelected;

  const BottomNavItem({
    required this.id,
    required this.label,
    required this.icon,
    required this.route,
    this.isSelected = false,
  });

  @override
  List<Object?> get props => [id, label, icon, route, isSelected];

  BottomNavItem copyWith({
    String? id,
    String? label,
    String? icon,
    String? route,
    bool? isSelected,
  }) {
    return BottomNavItem(
      id: id ?? this.id,
      label: label ?? this.label,
      icon: icon ?? this.icon,
      route: route ?? this.route,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
