import '../models/dashboard_model.dart';
import '../repository/dashboard_repository.dart';

class DashboardService {
  final DashboardRepository _repository;

  DashboardService(this._repository);

  /// Get default bottom navigation items
  List<BottomNavItem> getDefaultBottomNavItems() {
    return const [
      BottomNavItem(
        id: 'home',
        label: 'Home',
        icon: 'home',
        route: '/home',
        isSelected: true,
      ),
      BottomNavItem(
        id: 'family',
        label: 'Family',
        icon: 'family',
        route: '/family',
      ),
      BottomNavItem(
        id: 'events',
        label: 'Events',
        icon: 'events',
        route: '/events',
      ),
      BottomNavItem(
        id: 'recipes',
        label: 'Today\'s Special',
        icon: 'recipes',
        route: '/recipes',
      ),
      BottomNavItem(
        id: 'memories',
        label: 'Memories',
        icon: 'memories',
        route: '/memories',
      ),
      BottomNavItem(
        id: 'profile',
        label: 'Profile',
        icon: 'profile',
        route: '/profile',
      ),
    ];
  }

  /// Get default quick actions
  List<QuickAction> getDefaultQuickActions() {
    return const [
      QuickAction(
        id: 'add_family_member',
        title: 'Add Family Member',
        icon: 'person_add',
        route: '/family/add',
      ),
      QuickAction(
        id: 'create_event',
        title: 'Create Event',
        icon: 'event_add',
        route: '/events/create',
      ),
      QuickAction(
        id: 'upload_photo',
        title: 'Upload Photo',
        icon: 'photo_add',
        route: '/memories/upload',
      ),
      QuickAction(
        id: 'view_tree',
        title: 'Family Tree',
        icon: 'tree',
        route: '/family/tree',
      ),
    ];
  }

  /// Get default recent activities
  List<RecentActivity> getDefaultRecentActivities() {
    final now = DateTime.now();
    return [
      RecentActivity(
        id: '1',
        title: 'New family member added',
        description: 'Priya joined the family tree',
        timestamp: now.subtract(const Duration(hours: 2)),
        type: 'family',
      ),
      RecentActivity(
        id: '2',
        title: 'Onam celebration photos',
        description: '15 new photos uploaded',
        timestamp: now.subtract(const Duration(days: 1)),
        type: 'photos',
      ),
      RecentActivity(
        id: '3',
        title: 'Upcoming event reminder',
        description: 'Family get-together in 3 days',
        timestamp: now.subtract(const Duration(days: 2)),
        type: 'event',
      ),
    ];
  }

  /// Load dashboard data with user information
  Future<DashboardModel> loadDashboardData({
    String? userId,
    String? userName,
    String? userEmail,
    String? userPhoneNumber,
    String? userProfileImageUrl,
  }) async {
    try {
      final dashboardData = await _repository.getDashboardData();

      // Override with provided user data if available
      return dashboardData.copyWith(
        userId: userId ?? dashboardData.userId,
        userName: userName ?? dashboardData.userName,
        userEmail: userEmail,
        userPhoneNumber: userPhoneNumber,
        userProfileImageUrl: userProfileImageUrl,
      );
    } catch (e) {
      // Return default data if loading fails
      return DashboardModel(
        userId: userId ?? 'default_user',
        userName: userName ?? 'Family Member',
        userEmail: userEmail,
        userPhoneNumber: userPhoneNumber,
        userProfileImageUrl: userProfileImageUrl,
        quickActions: getDefaultQuickActions(),
        recentActivities: getDefaultRecentActivities(),
        currentTabIndex: 0,
      );
    }
  }

  /// Update selected navigation item
  Future<List<BottomNavItem>> updateSelectedNavItem(String itemId) async {
    try {
      return await _repository.updateSelectedNavItem(itemId);
    } catch (e) {
      // Return default items with updated selection
      return getDefaultBottomNavItems().map((item) {
        return item.copyWith(isSelected: item.id == itemId);
      }).toList();
    }
  }

  /// Refresh dashboard with user information
  Future<DashboardModel> refreshDashboard({
    String? userId,
    String? userName,
    String? userEmail,
    String? userPhoneNumber,
    String? userProfileImageUrl,
  }) async {
    try {
      final refreshedData = await _repository.refreshDashboard();

      // Override with provided user data if available
      return refreshedData.copyWith(
        userId: userId ?? refreshedData.userId,
        userName: userName ?? refreshedData.userName,
        userEmail: userEmail,
        userPhoneNumber: userPhoneNumber,
        userProfileImageUrl: userProfileImageUrl,
      );
    } catch (e) {
      return loadDashboardData(
        userId: userId,
        userName: userName,
        userEmail: userEmail,
        userPhoneNumber: userPhoneNumber,
        userProfileImageUrl: userProfileImageUrl,
      );
    }
  }

  /// Get navigation index by route
  int getNavIndexByRoute(String route) {
    final items = getDefaultBottomNavItems();
    for (int i = 0; i < items.length; i++) {
      if (items[i].route == route) {
        return i;
      }
    }
    return 0; // Default to home
  }

  /// Get route by navigation index
  String getRouteByNavIndex(int index) {
    final items = getDefaultBottomNavItems();
    if (index >= 0 && index < items.length) {
      return items[index].route;
    }
    return '/home'; // Default to home
  }
}
