import '../models/dashboard_model.dart';
import 'dashboard_repository.dart';

class MockDashboardRepository implements DashboardRepository {
  List<BottomNavItem> _bottomNavItems = [];
  int _currentTabIndex = 0;

  @override
  Future<DashboardModel> getDashboardData() async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    final now = DateTime.now();
    
    return DashboardModel(
      userId: 'mock_user_123',
      userName: 'Priya Nair',
      quickActions: [
        const QuickAction(
          id: 'add_family_member',
          title: 'Add Family Member',
          icon: 'person_add',
          route: '/family/add',
        ),
        const QuickAction(
          id: 'create_event',
          title: 'Create Event',
          icon: 'event_add',
          route: '/events/create',
        ),
        const QuickAction(
          id: 'upload_photo',
          title: 'Upload Photo',
          icon: 'photo_add',
          route: '/memories/upload',
        ),
        const QuickAction(
          id: 'view_tree',
          title: 'Family Tree',
          icon: 'tree',
          route: '/family/tree',
        ),
      ],
      recentActivities: [
        RecentActivity(
          id: '1',
          title: 'New family member added',
          description: '<PERSON><PERSON><PERSON> joined the family tree',
          timestamp: now.subtract(const Duration(hours: 2)),
          type: 'family',
        ),
        RecentActivity(
          id: '2',
          title: 'Onam celebration photos',
          description: '25 new photos uploaded from the celebration',
          timestamp: now.subtract(const Duration(days: 1)),
          type: 'photos',
        ),
        RecentActivity(
          id: '3',
          title: 'Upcoming event reminder',
          description: 'Family get-together in 3 days at Kochi',
          timestamp: now.subtract(const Duration(days: 2)),
          type: 'event',
        ),
        RecentActivity(
          id: '4',
          title: 'Birthday celebration',
          description: 'Grandmother\'s 80th birthday photos shared',
          timestamp: now.subtract(const Duration(days: 5)),
          type: 'photos',
        ),
        RecentActivity(
          id: '5',
          title: 'Family tree updated',
          description: 'Added 3 new generations to the family tree',
          timestamp: now.subtract(const Duration(days: 7)),
          type: 'family',
        ),
      ],
      currentTabIndex: _currentTabIndex,
    );
  }

  @override
  Future<List<QuickAction>> getQuickActions() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return const [
      QuickAction(
        id: 'add_family_member',
        title: 'Add Family Member',
        icon: 'person_add',
        route: '/family/add',
      ),
      QuickAction(
        id: 'create_event',
        title: 'Create Event',
        icon: 'event_add',
        route: '/events/create',
      ),
      QuickAction(
        id: 'upload_photo',
        title: 'Upload Photo',
        icon: 'photo_add',
        route: '/memories/upload',
      ),
      QuickAction(
        id: 'view_tree',
        title: 'Family Tree',
        icon: 'tree',
        route: '/family/tree',
      ),
    ];
  }

  @override
  Future<List<RecentActivity>> getRecentActivities() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    final now = DateTime.now();
    return [
      RecentActivity(
        id: '1',
        title: 'New family member added',
        description: 'Arjun joined the family tree',
        timestamp: now.subtract(const Duration(hours: 2)),
        type: 'family',
      ),
      RecentActivity(
        id: '2',
        title: 'Onam celebration photos',
        description: '25 new photos uploaded',
        timestamp: now.subtract(const Duration(days: 1)),
        type: 'photos',
      ),
    ];
  }

  @override
  Future<void> updateCurrentTab(int tabIndex) async {
    await Future.delayed(const Duration(milliseconds: 200));
    _currentTabIndex = tabIndex;
  }

  @override
  Future<DashboardModel> refreshDashboard() async {
    // Simulate longer refresh delay
    await Future.delayed(const Duration(seconds: 2));
    
    // Return updated data
    return getDashboardData();
  }

  @override
  Future<List<BottomNavItem>> getBottomNavItems() async {
    await Future.delayed(const Duration(milliseconds: 200));
    
    if (_bottomNavItems.isEmpty) {
      _bottomNavItems = const [
        BottomNavItem(
          id: 'home',
          label: 'Home',
          icon: 'home',
          route: '/home',
          isSelected: true,
        ),
        BottomNavItem(
          id: 'family',
          label: 'Family',
          icon: 'family',
          route: '/family',
        ),
        BottomNavItem(
          id: 'events',
          label: 'Events',
          icon: 'events',
          route: '/events',
        ),
        BottomNavItem(
          id: 'memories',
          label: 'Memories',
          icon: 'memories',
          route: '/memories',
        ),
        BottomNavItem(
          id: 'profile',
          label: 'Profile',
          icon: 'profile',
          route: '/profile',
        ),
      ];
    }
    
    return _bottomNavItems;
  }

  @override
  Future<List<BottomNavItem>> updateSelectedNavItem(String itemId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    
    _bottomNavItems = _bottomNavItems.map((item) {
      return item.copyWith(isSelected: item.id == itemId);
    }).toList();
    
    return _bottomNavItems;
  }
}
