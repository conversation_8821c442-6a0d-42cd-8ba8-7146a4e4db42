import '../models/dashboard_model.dart';

abstract class DashboardRepository {
  /// Get dashboard data for current user
  Future<DashboardModel> getDashboardData();
  
  /// Get quick actions for dashboard
  Future<List<QuickAction>> getQuickActions();
  
  /// Get recent activities
  Future<List<RecentActivity>> getRecentActivities();
  
  /// Update current tab index
  Future<void> updateCurrentTab(int tabIndex);
  
  /// Refresh dashboard data
  Future<DashboardModel> refreshDashboard();
  
  /// Get bottom navigation items
  Future<List<BottomNavItem>> getBottomNavItems();
  
  /// Update selected bottom nav item
  Future<List<BottomNavItem>> updateSelectedNavItem(String itemId);
}
