import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:google_fonts/google_fonts.dart';
import '../domain/models/dashboard_model.dart';

class DashboardWidget extends StatelessWidget {
  final DashboardModel dashboard;
  final bool isRefreshing;
  final VoidCallback onRefresh;
  final Function(String) onQuickActionTap;
  final Function(String) onRecentActivityTap;

  const DashboardWidget({
    super.key,
    required this.dashboard,
    this.isRefreshing = false,
    required this.onRefresh,
    required this.onQuickActionTap,
    required this.onRecentActivityTap,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async => onRefresh(),
      color: const Color(0xFF0D80F2),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(),
              
              // Quick Actions
              _buildQuickActions(),
              
              // Recent Activities
              _buildRecentActivities(),
              
              // Bottom spacing for navigation
              const SizedBox(height: 100),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Row(
        children: [
          // User Avatar
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: const Color(0xFFDBE8F2),
              border: Border.all(
                color: const Color(0xFFE8EDF5),
                width: 2,
              ),
            ),
            child: dashboard.userProfileImageUrl != null
                ? ClipOval(
                    child: Image.network(
                      dashboard.userProfileImageUrl!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildUserInitials();
                      },
                    ),
                  )
                : _buildUserInitials(),
          ),

          const SizedBox(width: 16),

          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'dashboard.welcomeBack'.tr(),
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    color: const Color(0xFF4A739C),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  dashboard.userName,
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w700,
                    fontSize: 24,
                    color: const Color(0xFF0D141C),
                  ),
                ),
                if (dashboard.userEmail != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    dashboard.userEmail!,
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      color: const Color(0xFF4A739C),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Notification Icon
          IconButton(
            onPressed: () {
              // TODO: Navigate to notifications
            },
            icon: const Icon(
              Icons.notifications_outlined,
              color: Color(0xFF4A739C),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInitials() {
    return Center(
      child: Text(
        dashboard.userInitials,
        style: GoogleFonts.plusJakartaSans(
          fontWeight: FontWeight.w700,
          fontSize: 24,
          color: const Color(0xFF4A739C),
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            'dashboard.quickActions'.tr(),
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color: const Color(0xFF0D141C),
            ),
          ),
        ),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: dashboard.quickActions.length,
            itemBuilder: (context, index) {
              final action = dashboard.quickActions[index];
              return Container(
                width: 100,
                margin: const EdgeInsets.only(right: 12),
                child: GestureDetector(
                  onTap: () => onQuickActionTap(action.id),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: const Color(0xFFDBE8F2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Icon(
                            _getIconForAction(action.icon),
                            size: 20,
                            color: const Color(0xFF0D141C),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          action.title,
                          style: GoogleFonts.plusJakartaSans(
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                            color: const Color(0xFF0D141C),
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRecentActivities() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Text(
            'dashboard.recentActivities'.tr(),
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color: const Color(0xFF0D141C),
            ),
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: dashboard.recentActivities.length,
          itemBuilder: (context, index) {
            final activity = dashboard.recentActivities[index];
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: GestureDetector(
                onTap: () => onRecentActivityTap(activity.id),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: const Color(0xFFE8EDF5),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          _getIconForActivityType(activity.type),
                          size: 24,
                          color: const Color(0xFF0D141C),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              activity.title,
                              style: GoogleFonts.plusJakartaSans(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                                color: const Color(0xFF0D141C),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              activity.description,
                              style: GoogleFonts.plusJakartaSans(
                                fontWeight: FontWeight.w400,
                                fontSize: 14,
                                color: const Color(0xFF4A739C),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _formatTimestamp(activity.timestamp),
                              style: GoogleFonts.plusJakartaSans(
                                fontWeight: FontWeight.w400,
                                fontSize: 12,
                                color: const Color(0xFF4A739C),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  IconData _getIconForAction(String iconName) {
    switch (iconName) {
      case 'person_add':
        return Icons.person_add;
      case 'event_add':
        return Icons.event;
      case 'photo_add':
        return Icons.photo_camera;
      case 'tree':
        return Icons.account_tree;
      default:
        return Icons.apps;
    }
  }

  IconData _getIconForActivityType(String type) {
    switch (type) {
      case 'family':
        return Icons.family_restroom;
      case 'photos':
        return Icons.photo_library;
      case 'event':
        return Icons.event;
      default:
        return Icons.notifications;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
}
