import 'package:equatable/equatable.dart';
import '../../domain/models/dashboard_model.dart';

abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

class DashboardInitial extends DashboardState {
  const DashboardInitial();
}

class DashboardLoading extends DashboardState {
  const DashboardLoading();
}

class DashboardLoaded extends DashboardState {
  final DashboardModel dashboard;
  final List<BottomNavItem> bottomNavItems;
  final int currentTabIndex;

  const DashboardLoaded({
    required this.dashboard,
    required this.bottomNavItems,
    this.currentTabIndex = 0,
  });

  @override
  List<Object?> get props => [dashboard, bottomNavItems, currentTabIndex];

  DashboardLoaded copyWith({
    DashboardModel? dashboard,
    List<BottomNavItem>? bottomNavItems,
    int? currentTabIndex,
  }) {
    return DashboardLoaded(
      dashboard: dashboard ?? this.dashboard,
      bottomNavItems: bottomNavItems ?? this.bottomNavItems,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
    );
  }
}

class DashboardError extends DashboardState {
  final String message;

  const DashboardError({required this.message});

  @override
  List<Object?> get props => [message];
}

class DashboardRefreshing extends DashboardState {
  final DashboardModel currentDashboard;
  final List<BottomNavItem> bottomNavItems;
  final int currentTabIndex;

  const DashboardRefreshing({
    required this.currentDashboard,
    required this.bottomNavItems,
    this.currentTabIndex = 0,
  });

  @override
  List<Object?> get props => [currentDashboard, bottomNavItems, currentTabIndex];
}

class DashboardTabChanged extends DashboardState {
  final DashboardModel dashboard;
  final List<BottomNavItem> bottomNavItems;
  final int newTabIndex;
  final String route;

  const DashboardTabChanged({
    required this.dashboard,
    required this.bottomNavItems,
    required this.newTabIndex,
    required this.route,
  });

  @override
  List<Object?> get props => [dashboard, bottomNavItems, newTabIndex, route];
}
