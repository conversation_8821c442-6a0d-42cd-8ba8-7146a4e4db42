import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/services/dashboard_service.dart';
import 'dashboard_state.dart';

class DashboardCubit extends Cubit<DashboardState> {
  final DashboardService _dashboardService;

  DashboardCubit(this._dashboardService) : super(const DashboardInitial());

  /// Load dashboard data with optional user information
  Future<void> loadDashboard({
    String? userId,
    String? userName,
    String? userEmail,
    String? userPhoneNumber,
    String? userProfileImageUrl,
  }) async {
    try {
      emit(const DashboardLoading());

      final dashboard = await _dashboardService.loadDashboardData(
        userId: userId,
        userName: userName,
        userEmail: userEmail,
        userPhoneNumber: userPhoneNumber,
        userProfileImageUrl: userProfileImageUrl,
      );
      final bottomNavItems = _dashboardService.getDefaultBottomNavItems();

      emit(DashboardLoaded(
        dashboard: dashboard,
        bottomNavItems: bottomNavItems,
        currentTabIndex: 0,
      ));
    } catch (e) {
      emit(DashboardError(message: e.toString()));
    }
  }

  /// Change bottom navigation tab
  Future<void> changeTab(int tabIndex) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      try {
        final route = _dashboardService.getRouteByNavIndex(tabIndex);
        final updatedNavItems = await _dashboardService.updateSelectedNavItem(
          _dashboardService.getDefaultBottomNavItems()[tabIndex].id,
        );
        
        emit(DashboardTabChanged(
          dashboard: currentState.dashboard,
          bottomNavItems: updatedNavItems,
          newTabIndex: tabIndex,
          route: route,
        ));
        
        // Update the loaded state with new tab
        emit(currentState.copyWith(
          bottomNavItems: updatedNavItems,
          currentTabIndex: tabIndex,
        ));
      } catch (e) {
        emit(DashboardError(message: e.toString()));
      }
    }
  }

  /// Refresh dashboard data
  Future<void> refreshDashboard() async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      try {
        emit(DashboardRefreshing(
          currentDashboard: currentState.dashboard,
          bottomNavItems: currentState.bottomNavItems,
          currentTabIndex: currentState.currentTabIndex,
        ));
        
        final refreshedDashboard = await _dashboardService.refreshDashboard();
        
        emit(currentState.copyWith(
          dashboard: refreshedDashboard,
        ));
      } catch (e) {
        emit(DashboardError(message: e.toString()));
        // Restore previous state on error
        emit(currentState);
      }
    }
  }

  /// Handle quick action tap
  void onQuickActionTap(String actionId) {
    // This will be handled by navigation in the UI layer
    // For now, just emit a debug message
    debugPrint('Quick action tapped: $actionId');
  }

  /// Handle recent activity tap
  void onRecentActivityTap(String activityId) {
    // This will be handled by navigation in the UI layer
    // For now, just emit a debug message
    debugPrint('Recent activity tapped: $activityId');
  }

  /// Reset to initial state
  void reset() {
    emit(const DashboardInitial());
  }

  /// Get current tab index
  int getCurrentTabIndex() {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      return currentState.currentTabIndex;
    }
    return 0;
  }

  /// Check if specific tab is selected
  bool isTabSelected(int tabIndex) {
    return getCurrentTabIndex() == tabIndex;
  }
}
