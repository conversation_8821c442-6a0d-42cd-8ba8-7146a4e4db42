import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../domain/models/recipe_model.dart';

class RecipeInfoSection extends StatelessWidget {
  final RecipeModel recipe;
  final Function(double)? onRatingTap;

  const RecipeInfoSection({
    super.key,
    required this.recipe,
    this.onRatingTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            recipe.title,
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 24,
              color: const Color(0xFF0D141C),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Description
          Text(
            recipe.description,
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w400,
              fontSize: 16,
              color: const Color(0xFF4F7396),
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Author Info
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.grey[200],
                backgroundImage: recipe.authorImageUrl != null
                    ? NetworkImage(recipe.authorImageUrl!)
                    : null,
                child: recipe.authorImageUrl == null
                    ? Icon(
                        Icons.person,
                        color: Colors.grey[600],
                      )
                    : null,
              ),
              
              const SizedBox(width: 12),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      recipe.authorName,
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                        color: const Color(0xFF0D141C),
                      ),
                    ),
                    Text(
                      'Recipe Author',
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                        color: const Color(0xFF4F7396),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Recipe Stats
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.access_time,
                  label: 'Prep Time',
                  value: '${recipe.preparationTimeMinutes}m',
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.schedule,
                  label: 'Cook Time',
                  value: '${recipe.cookingTimeMinutes}m',
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.people,
                  label: 'Servings',
                  value: '${recipe.servings}',
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.signal_cellular_alt,
                  label: 'Difficulty',
                  value: recipe.difficultyDisplayName,
                  valueColor: _getDifficultyColor(recipe.difficulty),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Rating Section
          Row(
            children: [
              // Current Rating
              if (recipe.rating > 0) ...[
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      color: Colors.amber[600],
                      size: 20,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      recipe.rating.toStringAsFixed(1),
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: const Color(0xFF0D141C),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '(${recipe.ratingsCount} reviews)',
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        color: const Color(0xFF4F7396),
                      ),
                    ),
                  ],
                ),
                
                const Spacer(),
              ],
              
              // Rate Recipe Button
              if (onRatingTap != null)
                TextButton.icon(
                  onPressed: () => _showRatingDialog(context),
                  icon: const Icon(
                    Icons.star_border,
                    size: 18,
                  ),
                  label: Text(
                    'Rate Recipe',
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFF0D80F2),
                  ),
                ),
            ],
          ),
          
          // Tags
          if (recipe.tags.isNotEmpty) ...[
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: recipe.tags.map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF7FAFC),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: const Color(0xFFE8EDF5),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    tag,
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                      color: const Color(0xFF4F7396),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: const Color(0xFF4F7396),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w400,
            fontSize: 12,
            color: const Color(0xFF4F7396),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w600,
            fontSize: 14,
            color: valueColor ?? const Color(0xFF0D141C),
          ),
        ),
      ],
    );
  }

  Color _getDifficultyColor(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return const Color(0xFF4CAF50);
      case DifficultyLevel.medium:
        return const Color(0xFFFF9800);
      case DifficultyLevel.hard:
        return const Color(0xFFF44336);
    }
  }

  void _showRatingDialog(BuildContext context) {
    double selectedRating = 0;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'Rate this Recipe',
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w600,
              fontSize: 18,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'How would you rate this recipe?',
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: const Color(0xFF4F7396),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  final rating = index + 1.0;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedRating = rating;
                      });
                    },
                    child: Icon(
                      selectedRating >= rating
                          ? Icons.star
                          : Icons.star_border,
                      color: Colors.amber[600],
                      size: 32,
                    ),
                  );
                }),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: selectedRating > 0
                  ? () {
                      Navigator.of(context).pop();
                      onRatingTap?.call(selectedRating);
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D80F2),
                foregroundColor: Colors.white,
              ),
              child: Text(
                'Submit',
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
