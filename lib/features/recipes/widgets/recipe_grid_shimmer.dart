import 'package:flutter/material.dart';
import 'recipe_card_shimmer.dart';

class RecipeGridShimmer extends StatelessWidget {
  final int itemCount;
  final bool showTitle;
  
  const RecipeGridShimmer({
    super.key,
    this.itemCount = 6,
    this.showTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'All Recipes',
              style: const TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 18,
                color: Color(0xFF0D141C),
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
        
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: itemCount,
            itemBuilder: (context, index) {
              return const RecipeCardShimmer();
            },
          ),
        ),
      ],
    );
  }
}
