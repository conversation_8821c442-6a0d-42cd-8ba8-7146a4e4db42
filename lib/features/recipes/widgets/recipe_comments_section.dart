import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../domain/models/comment_model.dart';

class RecipeCommentsSection extends StatelessWidget {
  final List<CommentModel> comments;
  final bool hasMoreComments;
  final bool isLoadingComments;
  final VoidCallback onLoadMoreComments;
  final Function(String, String) onReplyToComment;

  const RecipeCommentsSection({
    super.key,
    required this.comments,
    required this.hasMoreComments,
    required this.isLoadingComments,
    required this.onLoadMoreComments,
    required this.onReplyToComment,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Comments (${comments.length})',
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 20,
              color: const Color(0xFF0D141C),
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (comments.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.comment_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No comments yet',
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: const Color(0xFF4F7396),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Be the first to share your thoughts!',
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        color: const Color(0xFF4F7396),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ...comments.map((comment) => _buildCommentItem(context, comment)),
          
          // Load More Button
          if (hasMoreComments && !isLoadingComments)
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Center(
                child: TextButton(
                  onPressed: onLoadMoreComments,
                  child: Text(
                    'Load More Comments',
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: const Color(0xFF0D80F2),
                    ),
                  ),
                ),
              ),
            ),
          
          // Loading Indicator
          if (isLoadingComments)
            const Padding(
              padding: EdgeInsets.only(top: 16),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCommentItem(BuildContext context, CommentModel comment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Comment Header
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: Colors.grey[200],
                backgroundImage: comment.authorImageUrl != null
                    ? NetworkImage(comment.authorImageUrl!)
                    : null,
                child: comment.authorImageUrl == null
                    ? Icon(
                        Icons.person,
                        size: 16,
                        color: Colors.grey[600],
                      )
                    : null,
              ),
              
              const SizedBox(width: 8),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      comment.authorName,
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                        color: const Color(0xFF0D141C),
                      ),
                    ),
                    Text(
                      _formatDate(comment.createdAt),
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                        color: const Color(0xFF4F7396),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Rating if present
              if (comment.hasRating) ...[
                Icon(
                  Icons.star,
                  size: 16,
                  color: Colors.amber[600],
                ),
                const SizedBox(width: 2),
                Text(
                  comment.rating!.toStringAsFixed(1),
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                    color: const Color(0xFF0D141C),
                  ),
                ),
              ],
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Comment Content
          Padding(
            padding: const EdgeInsets.only(left: 40),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  comment.content,
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    color: const Color(0xFF0D141C),
                    height: 1.4,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // Reply Button
                GestureDetector(
                  onTap: () => _showReplyDialog(context, comment),
                  child: Text(
                    'Reply',
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                      color: const Color(0xFF0D80F2),
                    ),
                  ),
                ),
                
                // Replies
                if (comment.replies.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  ...comment.replies.map((reply) => _buildReplyItem(context, reply)),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReplyItem(BuildContext context, CommentModel reply) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8, left: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF7FAFC),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 12,
                backgroundColor: Colors.grey[200],
                backgroundImage: reply.authorImageUrl != null
                    ? NetworkImage(reply.authorImageUrl!)
                    : null,
                child: reply.authorImageUrl == null
                    ? Icon(
                        Icons.person,
                        size: 12,
                        color: Colors.grey[600],
                      )
                    : null,
              ),
              
              const SizedBox(width: 6),
              
              Text(
                reply.authorName,
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  color: const Color(0xFF0D141C),
                ),
              ),
              
              const SizedBox(width: 8),
              
              Text(
                _formatDate(reply.createdAt),
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 10,
                  color: const Color(0xFF4F7396),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 6),
          
          Text(
            reply.content,
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w400,
              fontSize: 12,
              color: const Color(0xFF0D141C),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  void _showReplyDialog(BuildContext context, CommentModel comment) {
    final TextEditingController replyController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Reply to ${comment.authorName}',
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        content: TextField(
          controller: replyController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Write your reply...',
            hintStyle: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              color: const Color(0xFF4F7396),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w400,
            fontSize: 14,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              if (replyController.text.trim().isNotEmpty) {
                Navigator.of(context).pop();
                onReplyToComment(comment.id, replyController.text.trim());
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0D80F2),
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Reply',
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
