import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../domain/models/recipe_model.dart';

class RecipeCategoryFilter extends StatelessWidget {
  final RecipeCategory? selectedCategory;
  final Function(RecipeCategory?) onCategorySelected;

  const RecipeCategoryFilter({
    super.key,
    required this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final categories = [
      null, // All categories
      ...RecipeCategory.values,
    ];

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Categories',
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: const Color(0xFF0D141C),
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                final isSelected = selectedCategory == category;
                
                return GestureDetector(
                  onTap: () => onCategorySelected(category),
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? const Color(0xFF0D80F2) 
                          : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected 
                            ? const Color(0xFF0D80F2) 
                            : const Color(0xFFE8EDF5),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        category?.categoryDisplayName ?? 'All',
                        style: GoogleFonts.plusJakartaSans(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                          color: isSelected 
                              ? Colors.white 
                              : const Color(0xFF4F7396),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

extension RecipeCategoryExtension on RecipeCategory {
  String get categoryDisplayName {
    switch (this) {
      case RecipeCategory.breakfast:
        return 'Breakfast';
      case RecipeCategory.lunch:
        return 'Lunch';
      case RecipeCategory.dinner:
        return 'Dinner';
      case RecipeCategory.snacks:
        return 'Snacks';
      case RecipeCategory.desserts:
        return 'Desserts';
      case RecipeCategory.beverages:
        return 'Beverages';
      case RecipeCategory.traditional:
        return 'Traditional';
      case RecipeCategory.modern:
        return 'Modern';
    }
  }
}
