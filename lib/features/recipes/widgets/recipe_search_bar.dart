import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class RecipeSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final Function(String) onSearch;
  final VoidCallback onClear;

  const RecipeSearchBar({
    super.key,
    required this.controller,
    required this.onSearch,
    required this.onClear,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF7FAFC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE8EDF5),
          width: 1,
        ),
      ),
      child: TextField(
        controller: controller,
        onSubmitted: onSearch,
        decoration: InputDecoration(
          hintText: 'Search recipes...',
          hintStyle: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w400,
            fontSize: 14,
            color: const Color(0xFF4F7396),
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: Color(0xFF4F7396),
            size: 20,
          ),
          suffixIcon: controller.text.isNotEmpty
              ? IconButton(
                  onPressed: onClear,
                  icon: const Icon(
                    Icons.clear,
                    color: Color(0xFF4F7396),
                    size: 20,
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        style: GoogleFonts.plusJakartaSans(
          fontWeight: FontWeight.w400,
          fontSize: 14,
          color: const Color(0xFF0D141C),
        ),
      ),
    );
  }
}
