import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class RecipeInstructionsSection extends StatelessWidget {
  final String instructions;

  const RecipeInstructionsSection({
    super.key,
    required this.instructions,
  });

  @override
  Widget build(BuildContext context) {
    // Split instructions by newlines or numbered steps
    final steps = _parseInstructions(instructions);
    
    return Container(
      color: Colors.white,
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Instructions',
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 20,
              color: const Color(0xFF0D141C),
            ),
          ),
          
          const SizedBox(height: 16),
          
          ...steps.asMap().entries.map((entry) {
            final index = entry.key;
            final step = entry.value;
            
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: const Color(0xFF0D80F2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: GoogleFonts.plusJakartaSans(
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  
                  Expanded(
                    child: Text(
                      step,
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        color: const Color(0xFF0D141C),
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  List<String> _parseInstructions(String instructions) {
    // First try to split by numbered steps (1., 2., etc.)
    final numberedSteps = instructions.split(RegExp(r'\d+\.\s*'));
    if (numberedSteps.length > 1) {
      return numberedSteps
          .where((step) => step.trim().isNotEmpty)
          .map((step) => step.trim())
          .toList();
    }

    // If no numbered steps, split by newlines
    final lines = instructions.split('\n');
    if (lines.length > 1) {
      return lines
          .where((line) => line.trim().isNotEmpty)
          .map((line) => line.trim())
          .toList();
    }

    // If it's just one long instruction, return as is
    return [instructions.trim()];
  }
}
