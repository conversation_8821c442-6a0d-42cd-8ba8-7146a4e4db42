import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../logic/recipe/recipe_cubit.dart';
import '../logic/recipe/recipe_state.dart';
import '../domain/services/recipe_service.dart';
import '../domain/repository/supabase_recipe_repository.dart';

import '../widgets/recipe_card.dart';
import '../widgets/todays_special_carousel.dart';
import '../widgets/recipe_category_filter.dart';
import '../widgets/recipe_search_bar.dart';
import '../widgets/recipes_shimmer.dart';
import '../widgets/recipe_card_shimmer.dart';
import 'recipe_detail_screen.dart';
import '../../../core/network/supabase_client.dart';


class RecipesScreen extends StatelessWidget {
  const RecipesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RecipeCubit(
        RecipeService(
          SupabaseRecipeRepository(SupabaseNetworkClient()),
        ),
      )..loadRecipes(),
      child: const RecipesView(),
    );
  }
}

class RecipesView extends StatefulWidget {
  const RecipesView({super.key});

  @override
  State<RecipesView> createState() => _RecipesViewState();
}

class _RecipesViewState extends State<RecipesView> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<RecipeCubit>().loadMoreRecipes();
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7FAFC),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFE8EDF5),
                    width: 1,
                  ),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Today\'s Special',
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w700,
                      fontSize: 24,
                      color: const Color(0xFF0D141C),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Discover delicious recipes and cooking tips',
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      color: const Color(0xFF4F7396),
                    ),
                  ),
                  const SizedBox(height: 16),
                  RecipeSearchBar(
                    controller: _searchController,
                    onSearch: (query) {
                      context.read<RecipeCubit>().searchRecipes(query);
                    },
                    onClear: () {
                      _searchController.clear();
                      context.read<RecipeCubit>().clearFilters();
                    },
                  ),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: BlocBuilder<RecipeCubit, RecipeState>(
                builder: (context, state) {
                  if (state is RecipeLoading) {
                    return const RecipesShimmer();
                  }
                  
                  if (state is RecipeError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Something went wrong',
                            style: GoogleFonts.plusJakartaSans(
                              fontWeight: FontWeight.w600,
                              fontSize: 18,
                              color: const Color(0xFF0D141C),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.message,
                            textAlign: TextAlign.center,
                            style: GoogleFonts.plusJakartaSans(
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              color: const Color(0xFF4F7396),
                            ),
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton(
                            onPressed: () {
                              context.read<RecipeCubit>().loadRecipes();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0D80F2),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Try Again',
                              style: GoogleFonts.plusJakartaSans(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                  
                  if (state is RecipeLoaded || state is RecipeLoadingMore) {
                    final recipes = state is RecipeLoaded 
                        ? state.recipes 
                        : (state as RecipeLoadingMore).currentRecipes;
                    final todaysSpecial = state is RecipeLoaded 
                        ? state.todaysSpecial 
                        : (state as RecipeLoadingMore).todaysSpecial;
                    
                    return RefreshIndicator(
                      onRefresh: () async {
                        await context.read<RecipeCubit>().refreshRecipes();
                      },
                      child: CustomScrollView(
                        controller: _scrollController,
                        slivers: [
                          // Today's Special Carousel
                          if (todaysSpecial.isNotEmpty)
                            SliverToBoxAdapter(
                              child: TodaysSpecialCarousel(
                                recipes: todaysSpecial,
                                onRecipeTap: (recipe) {
                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder: (context) => RecipeDetailScreen(
                                        recipeId: recipe.id,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          
                          // Category Filter
                          SliverToBoxAdapter(
                            child: RecipeCategoryFilter(
                              selectedCategory: state is RecipeLoaded 
                                  ? state.selectedCategory 
                                  : (state as RecipeLoadingMore).selectedCategory,
                              onCategorySelected: (category) {
                                context.read<RecipeCubit>().filterByCategory(category);
                              },
                            ),
                          ),
                          
                          // Recipes Grid
                          if (recipes.isEmpty)
                            SliverFillRemaining(
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.restaurant_menu,
                                      size: 64,
                                      color: Colors.grey[400],
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'No recipes found',
                                      style: GoogleFonts.plusJakartaSans(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 18,
                                        color: const Color(0xFF0D141C),
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Try adjusting your search or filters',
                                      style: GoogleFonts.plusJakartaSans(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 14,
                                        color: const Color(0xFF4F7396),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                          else
                            SliverPadding(
                              padding: const EdgeInsets.all(16),
                              sliver: SliverGrid(
                                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  childAspectRatio: 0.75,
                                  crossAxisSpacing: 12,
                                  mainAxisSpacing: 12,
                                ),
                                delegate: SliverChildBuilderDelegate(
                                  (context, index) {
                                    if (index >= recipes.length) {
                                      return const RecipeCardShimmer();
                                    }
                                    
                                    final recipe = recipes[index];
                                    return RecipeCard(
                                      recipe: recipe,
                                      onTap: () {
                                        Navigator.of(context).push(
                                          MaterialPageRoute(
                                            builder: (context) => RecipeDetailScreen(
                                              recipeId: recipe.id,
                                            ),
                                          ),
                                        );
                                      },
                                    );
                                  },
                                  childCount: recipes.length + 
                                      (state is RecipeLoadingMore ? 1 : 0),
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  }
                  
                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
