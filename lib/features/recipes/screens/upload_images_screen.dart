import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/upload_sample_images.dart';

/// Screen to upload sample images to Supabase storage
/// This is a utility screen for development/testing purposes
class UploadImagesScreen extends StatefulWidget {
  const UploadImagesScreen({super.key});

  @override
  State<UploadImagesScreen> createState() => _UploadImagesScreenState();
}

class _UploadImagesScreenState extends State<UploadImagesScreen> {
  bool _isUploading = false;
  String _status = 'Ready to upload sample images';
  final List<String> _logs = [];

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
    print(message);
  }

  Future<void> _uploadImages() async {
    if (_isUploading) return;

    setState(() {
      _isUploading = true;
      _status = 'Uploading images...';
      _logs.clear();
    });

    try {
      _addLog('🔧 Checking storage bucket access...');
      
      final bucketAccessible = await UploadSampleImages.checkStorageBucket();
      
      if (!bucketAccessible) {
        _addLog('❌ Cannot access recipe-images bucket');
        _addLog('Please check bucket exists and has proper permissions');
        setState(() {
          _status = 'Error: Cannot access storage bucket';
          _isUploading = false;
        });
        return;
      }

      _addLog('✅ Storage bucket is accessible');
      _addLog('🚀 Starting image upload process...');

      // Upload all sample images
      final uploadedImages = await UploadSampleImages.uploadAllSampleImages();
      
      if (uploadedImages.isNotEmpty) {
        _addLog('📝 Updating recipes with storage URLs...');
        await UploadSampleImages.updateRecipesWithStorageUrls(uploadedImages);
        
        _addLog('🎉 Successfully uploaded ${uploadedImages.length} images');
        setState(() {
          _status = 'Upload completed successfully!';
        });
      } else {
        _addLog('❌ No images were uploaded');
        setState(() {
          _status = 'Upload failed - no images uploaded';
        });
      }
    } catch (e) {
      _addLog('💥 Error during upload: $e');
      setState(() {
        _status = 'Upload failed with error';
      });
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7FAFC),
      appBar: AppBar(
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF0D141C),
        elevation: 0,
        title: Text(
          'Upload Sample Images',
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            color: const Color(0xFF0D141C),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Status',
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: const Color(0xFF0D141C),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      if (_isUploading) ...[
                        const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        const SizedBox(width: 8),
                      ],
                      Expanded(
                        child: Text(
                          _status,
                          style: GoogleFonts.plusJakartaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                            color: const Color(0xFF4F7396),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Upload Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isUploading ? null : _uploadImages,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0D80F2),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  _isUploading ? 'Uploading...' : 'Upload Sample Images',
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Logs Section
            Text(
              'Upload Logs',
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: const Color(0xFF0D141C),
              ),
            ),
            
            const SizedBox(height: 8),
            
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: _logs.isEmpty
                    ? Text(
                        'No logs yet...',
                        style: GoogleFonts.plusJakartaSans(
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: const Color(0xFF4F7396),
                        ),
                      )
                    : ListView.builder(
                        itemCount: _logs.length,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              _logs[index],
                              style: GoogleFonts.robotoMono(
                                fontSize: 12,
                                color: const Color(0xFF0D141C),
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Info Text
            Text(
              'This will download sample food images from Unsplash and upload them to your Supabase storage bucket, then update the recipes database with the new URLs.',
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w400,
                fontSize: 12,
                color: const Color(0xFF4F7396),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
