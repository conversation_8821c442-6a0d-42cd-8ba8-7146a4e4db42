import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../logic/recipe_detail/recipe_detail_cubit.dart';
import '../logic/recipe/recipe_state.dart';
import '../domain/services/recipe_service.dart';
import '../domain/repository/supabase_recipe_repository.dart';
import '../domain/models/recipe_model.dart';
import '../widgets/recipe_image_carousel.dart';
import '../widgets/recipe_info_section.dart';
import '../widgets/recipe_ingredients_section.dart';
import '../widgets/recipe_instructions_section.dart';
import '../widgets/recipe_comments_section.dart';
import '../widgets/add_comment_bottom_sheet.dart';
import '../widgets/recipe_detail_shimmer.dart';
import '../../../core/network/supabase_client.dart';


class RecipeDetailScreen extends StatelessWidget {
  final String recipeId;

  const RecipeDetailScreen({
    super.key,
    required this.recipeId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RecipeDetailCubit(
        RecipeService(
          SupabaseRecipeRepository(SupabaseNetworkClient()),
        ),
      )..loadRecipeDetail(recipeId),
      child: RecipeDetailView(recipeId: recipeId),
    );
  }
}

class RecipeDetailView extends StatefulWidget {
  final String recipeId;

  const RecipeDetailView({
    super.key,
    required this.recipeId,
  });

  @override
  State<RecipeDetailView> createState() => _RecipeDetailViewState();
}

class _RecipeDetailViewState extends State<RecipeDetailView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<RecipeDetailCubit>().loadMoreComments();
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  void _showAddCommentBottomSheet(BuildContext context, RecipeModel recipe) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddCommentBottomSheet(
        recipe: recipe,
        onCommentAdded: (content, rating) {
          context.read<RecipeDetailCubit>().addComment(
                content: content,
                rating: rating,
              );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7FAFC),
      body: BlocBuilder<RecipeDetailCubit, RecipeDetailState>(
        builder: (context, state) {
          if (state is RecipeDetailLoading) {
            return const RecipeDetailShimmer();
          }

          if (state is RecipeDetailError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Recipe not found',
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                      color: const Color(0xFF0D141C),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      color: const Color(0xFF4F7396),
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0D80F2),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Go Back',
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          if (state is RecipeDetailLoaded ||
              state is RecipeDetailCommentAdded ||
              state is RecipeDetailRatingUpdated) {
            final recipe = state is RecipeDetailLoaded
                ? state.recipe
                : state is RecipeDetailCommentAdded
                    ? state.recipe
                    : (state as RecipeDetailRatingUpdated).recipe;

            final comments = state is RecipeDetailLoaded
                ? state.comments
                : state is RecipeDetailCommentAdded
                    ? state.comments
                    : (state as RecipeDetailRatingUpdated).comments;

            return CustomScrollView(
              controller: _scrollController,
              slivers: [
                // App Bar
                SliverAppBar(
                  expandedHeight: 0,
                  floating: true,
                  pinned: true,
                  backgroundColor: Colors.white,
                  foregroundColor: const Color(0xFF0D141C),
                  elevation: 0,
                  title: Text(
                    recipe.title,
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                      color: const Color(0xFF0D141C),
                    ),
                  ),
                  actions: [
                    IconButton(
                      onPressed: () {
                        // TODO: Implement share functionality
                      },
                      icon: const Icon(Icons.share),
                    ),
                    IconButton(
                      onPressed: () {
                        // TODO: Implement favorite functionality
                      },
                      icon: const Icon(Icons.favorite_border),
                    ),
                  ],
                ),

                // Recipe Image Carousel
                SliverToBoxAdapter(
                  child: RecipeImageCarousel(
                    imageUrls: recipe.imageUrls,
                  ),
                ),

                // Recipe Info
                SliverToBoxAdapter(
                  child: RecipeInfoSection(
                    recipe: recipe,
                    onRatingTap: (rating) {
                      context.read<RecipeDetailCubit>().rateRecipe(rating);
                    },
                  ),
                ),

                // Ingredients
                SliverToBoxAdapter(
                  child: RecipeIngredientsSection(
                    ingredients: recipe.ingredients,
                  ),
                ),

                // Instructions
                SliverToBoxAdapter(
                  child: RecipeInstructionsSection(
                    instructions: recipe.instructions,
                  ),
                ),

                // Comments Section
                SliverToBoxAdapter(
                  child: RecipeCommentsSection(
                    comments: comments,
                    hasMoreComments: state is RecipeDetailLoaded
                        ? state.hasMoreComments
                        : false,
                    isLoadingComments: state is RecipeDetailLoaded
                        ? state.isLoadingComments
                        : false,
                    onLoadMoreComments: () {
                      context.read<RecipeDetailCubit>().loadMoreComments();
                    },
                    onReplyToComment: (parentCommentId, content) {
                      context.read<RecipeDetailCubit>().addComment(
                            content: content,
                            parentCommentId: parentCommentId,
                          );
                    },
                  ),
                ),

                // Bottom padding
                const SliverToBoxAdapter(
                  child: SizedBox(height: 100),
                ),
              ],
            );
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: BlocBuilder<RecipeDetailCubit, RecipeDetailState>(
        builder: (context, state) {
          if (state is RecipeDetailLoaded ||
              state is RecipeDetailCommentAdded ||
              state is RecipeDetailRatingUpdated) {
            final recipe = state is RecipeDetailLoaded
                ? state.recipe
                : state is RecipeDetailCommentAdded
                    ? state.recipe
                    : (state as RecipeDetailRatingUpdated).recipe;

            return FloatingActionButton.extended(
              onPressed: () => _showAddCommentBottomSheet(context, recipe),
              backgroundColor: const Color(0xFF0D80F2),
              foregroundColor: Colors.white,
              icon: const Icon(Icons.comment),
              label: Text(
                'Add Comment',
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }
}
