import 'package:equatable/equatable.dart';
import '../../domain/models/recipe_model.dart';
import '../../domain/models/comment_model.dart';

abstract class RecipeState extends Equatable {
  const RecipeState();

  @override
  List<Object?> get props => [];
}

class RecipeInitial extends RecipeState {
  const RecipeInitial();
}

class RecipeLoading extends RecipeState {
  const RecipeLoading();
}

class RecipeLoaded extends RecipeState {
  final List<RecipeModel> recipes;
  final List<RecipeModel> todaysSpecial;
  final bool hasReachedMax;
  final int currentPage;
  final RecipeCategory? selectedCategory;
  final String? searchQuery;

  const RecipeLoaded({
    required this.recipes,
    required this.todaysSpecial,
    this.hasReachedMax = false,
    this.currentPage = 1,
    this.selectedCategory,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
        recipes,
        todaysSpecial,
        hasReachedMax,
        currentPage,
        selectedCategory,
        searchQuery,
      ];

  RecipeLoaded copyWith({
    List<RecipeModel>? recipes,
    List<RecipeModel>? todaysSpecial,
    bool? hasReachedMax,
    int? currentPage,
    RecipeCategory? selectedCategory,
    String? searchQuery,
    bool clearCategory = false,
    bool clearSearch = false,
  }) {
    return RecipeLoaded(
      recipes: recipes ?? this.recipes,
      todaysSpecial: todaysSpecial ?? this.todaysSpecial,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      selectedCategory: clearCategory ? null : (selectedCategory ?? this.selectedCategory),
      searchQuery: clearSearch ? null : (searchQuery ?? this.searchQuery),
    );
  }
}

class RecipeError extends RecipeState {
  final String message;

  const RecipeError({required this.message});

  @override
  List<Object?> get props => [message];
}

class RecipeLoadingMore extends RecipeState {
  final List<RecipeModel> currentRecipes;
  final List<RecipeModel> todaysSpecial;
  final int currentPage;
  final RecipeCategory? selectedCategory;
  final String? searchQuery;

  const RecipeLoadingMore({
    required this.currentRecipes,
    required this.todaysSpecial,
    required this.currentPage,
    this.selectedCategory,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
        currentRecipes,
        todaysSpecial,
        currentPage,
        selectedCategory,
        searchQuery,
      ];
}

// Recipe Detail States
abstract class RecipeDetailState extends Equatable {
  const RecipeDetailState();

  @override
  List<Object?> get props => [];
}

class RecipeDetailInitial extends RecipeDetailState {
  const RecipeDetailInitial();
}

class RecipeDetailLoading extends RecipeDetailState {
  const RecipeDetailLoading();
}

class RecipeDetailLoaded extends RecipeDetailState {
  final RecipeModel recipe;
  final List<CommentModel> comments;
  final bool hasMoreComments;
  final int currentCommentPage;
  final bool isLoadingComments;

  const RecipeDetailLoaded({
    required this.recipe,
    required this.comments,
    this.hasMoreComments = true,
    this.currentCommentPage = 1,
    this.isLoadingComments = false,
  });

  @override
  List<Object?> get props => [
        recipe,
        comments,
        hasMoreComments,
        currentCommentPage,
        isLoadingComments,
      ];

  RecipeDetailLoaded copyWith({
    RecipeModel? recipe,
    List<CommentModel>? comments,
    bool? hasMoreComments,
    int? currentCommentPage,
    bool? isLoadingComments,
  }) {
    return RecipeDetailLoaded(
      recipe: recipe ?? this.recipe,
      comments: comments ?? this.comments,
      hasMoreComments: hasMoreComments ?? this.hasMoreComments,
      currentCommentPage: currentCommentPage ?? this.currentCommentPage,
      isLoadingComments: isLoadingComments ?? this.isLoadingComments,
    );
  }
}

class RecipeDetailError extends RecipeDetailState {
  final String message;

  const RecipeDetailError({required this.message});

  @override
  List<Object?> get props => [message];
}

class RecipeDetailCommentAdded extends RecipeDetailState {
  final RecipeModel recipe;
  final List<CommentModel> comments;
  final CommentModel newComment;

  const RecipeDetailCommentAdded({
    required this.recipe,
    required this.comments,
    required this.newComment,
  });

  @override
  List<Object?> get props => [recipe, comments, newComment];
}

class RecipeDetailRatingUpdated extends RecipeDetailState {
  final RecipeModel recipe;
  final List<CommentModel> comments;

  const RecipeDetailRatingUpdated({
    required this.recipe,
    required this.comments,
  });

  @override
  List<Object?> get props => [recipe, comments];
}
