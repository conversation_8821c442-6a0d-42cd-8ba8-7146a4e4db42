import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/models/recipe_model.dart';
import '../../domain/services/recipe_service.dart';
import 'recipe_state.dart';

class RecipeCubit extends Cubit<RecipeState> {
  final RecipeService _recipeService;
  static const int _pageSize = 10;

  RecipeCubit(this._recipeService) : super(const RecipeInitial());

  /// Load initial recipes and today's special
  Future<void> loadRecipes() async {
    try {
      emit(const RecipeLoading());

      final futures = await Future.wait([
        _recipeService.getRecipes(page: 1, limit: _pageSize),
        _recipeService.getTodaysSpecialRecipes(),
      ]);

      final recipes = futures[0] as List<RecipeModel>;
      final todaysSpecial = futures[1] as List<RecipeModel>;

      emit(RecipeLoaded(
        recipes: recipes,
        todaysSpecial: todaysSpecial,
        hasReachedMax: recipes.length < _pageSize,
        currentPage: 1,
      ));
    } catch (e) {
      emit(RecipeError(message: e.toString()));
    }
  }

  /// Load more recipes (pagination)
  Future<void> loadMoreRecipes() async {
    final currentState = state;
    if (currentState is! RecipeLoaded || currentState.hasReachedMax) {
      return;
    }

    try {
      emit(RecipeLoadingMore(
        currentRecipes: currentState.recipes,
        todaysSpecial: currentState.todaysSpecial,
        currentPage: currentState.currentPage,
        selectedCategory: currentState.selectedCategory,
        searchQuery: currentState.searchQuery,
      ));

      final nextPage = currentState.currentPage + 1;
      final newRecipes = await _recipeService.getRecipes(
        page: nextPage,
        limit: _pageSize,
        category: currentState.selectedCategory,
        searchQuery: currentState.searchQuery,
      );

      final allRecipes = [...currentState.recipes, ...newRecipes];

      emit(RecipeLoaded(
        recipes: allRecipes,
        todaysSpecial: currentState.todaysSpecial,
        hasReachedMax: newRecipes.length < _pageSize,
        currentPage: nextPage,
        selectedCategory: currentState.selectedCategory,
        searchQuery: currentState.searchQuery,
      ));
    } catch (e) {
      emit(RecipeError(message: e.toString()));
    }
  }

  /// Filter recipes by category
  Future<void> filterByCategory(RecipeCategory? category) async {
    try {
      emit(const RecipeLoading());

      final futures = await Future.wait([
        _recipeService.getRecipes(
          page: 1,
          limit: _pageSize,
          category: category,
        ),
        _recipeService.getTodaysSpecialRecipes(),
      ]);

      final recipes = futures[0] as List<RecipeModel>;
      final todaysSpecial = futures[1] as List<RecipeModel>;

      emit(RecipeLoaded(
        recipes: recipes,
        todaysSpecial: todaysSpecial,
        hasReachedMax: recipes.length < _pageSize,
        currentPage: 1,
        selectedCategory: category,
      ));
    } catch (e) {
      emit(RecipeError(message: e.toString()));
    }
  }

  /// Search recipes
  Future<void> searchRecipes(String query) async {
    try {
      emit(const RecipeLoading());

      final futures = await Future.wait([
        _recipeService.searchRecipes(query: query, page: 1, limit: _pageSize),
        _recipeService.getTodaysSpecialRecipes(),
      ]);

      final recipes = futures[0] as List<RecipeModel>;
      final todaysSpecial = futures[1] as List<RecipeModel>;

      emit(RecipeLoaded(
        recipes: recipes,
        todaysSpecial: todaysSpecial,
        hasReachedMax: recipes.length < _pageSize,
        currentPage: 1,
        searchQuery: query.isNotEmpty ? query : null,
      ));
    } catch (e) {
      emit(RecipeError(message: e.toString()));
    }
  }

  /// Clear filters and search
  Future<void> clearFilters() async {
    await loadRecipes();
  }

  /// Refresh recipes
  Future<void> refreshRecipes() async {
    await loadRecipes();
  }

  /// Get recipe categories for filtering
  List<RecipeCategory> getCategories() {
    return _recipeService.getRecipeCategories();
  }
}
