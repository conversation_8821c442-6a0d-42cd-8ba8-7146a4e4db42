import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'recipe_creation_state.dart';
import '../../domain/services/recipe_service.dart';
import '../../domain/models/recipe_model.dart';

class RecipeCreationCubit extends Cubit<RecipeCreationState> {
  final RecipeService _recipeService;

  RecipeCreationCubit(this._recipeService) : super(const RecipeCreationInitial());

  /// Create a new recipe with images
  Future<void> createRecipe({
    required CreateRecipeRequest request,
    required List<File> imageFiles,
  }) async {
    try {
      emit(const RecipeCreationLoading());

      // Upload images first
      final imageUrls = <String>[];
      
      for (int i = 0; i < imageFiles.length; i++) {
        emit(RecipeCreationImageUploading(
          uploadedCount: i,
          totalCount: imageFiles.length,
        ));
        
        final imageUrl = await _recipeService.uploadRecipeImage(imageFiles[i].path);
        imageUrls.add(imageUrl);
      }

      // Create recipe with uploaded image URLs
      final updatedRequest = CreateRecipeRequest(
        title: request.title,
        description: request.description,
        imageUrls: imageUrls,
        instructions: request.instructions,
        ingredients: request.ingredients,
        preparationTimeMinutes: request.preparationTimeMinutes,
        cookingTimeMinutes: request.cookingTimeMinutes,
        servings: request.servings,
        category: request.category,
        difficulty: request.difficulty,
        tags: request.tags,
      );

      final recipe = await _recipeService.createRecipe(updatedRequest);
      
      emit(RecipeCreationSuccess(recipe: recipe));
    } catch (e) {
      emit(RecipeCreationError(message: e.toString()));
    }
  }

  /// Reset the state
  void reset() {
    emit(const RecipeCreationInitial());
  }
}
