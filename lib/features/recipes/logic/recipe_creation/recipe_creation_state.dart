import 'package:equatable/equatable.dart';
import '../../domain/models/recipe_model.dart';

abstract class RecipeCreationState extends Equatable {
  const RecipeCreationState();

  @override
  List<Object?> get props => [];
}

class RecipeCreationInitial extends RecipeCreationState {
  const RecipeCreationInitial();
}

class RecipeCreationLoading extends RecipeCreationState {
  const RecipeCreationLoading();
}

class RecipeCreationSuccess extends RecipeCreationState {
  final RecipeModel recipe;

  const RecipeCreationSuccess({required this.recipe});

  @override
  List<Object?> get props => [recipe];
}

class RecipeCreationError extends RecipeCreationState {
  final String message;

  const RecipeCreationError({required this.message});

  @override
  List<Object?> get props => [message];
}

class RecipeCreationImageUploading extends RecipeCreationState {
  final int uploadedCount;
  final int totalCount;

  const RecipeCreationImageUploading({
    required this.uploadedCount,
    required this.totalCount,
  });

  @override
  List<Object?> get props => [uploadedCount, totalCount];
}
