import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/models/recipe_model.dart';
import '../../domain/models/comment_model.dart';
import '../../domain/services/recipe_service.dart';
import '../recipe/recipe_state.dart';

class RecipeDetailCubit extends Cubit<RecipeDetailState> {
  final RecipeService _recipeService;
  static const int _commentsPageSize = 20;

  RecipeDetailCubit(this._recipeService) : super(const RecipeDetailInitial());

  /// Load recipe details and comments
  Future<void> loadRecipeDetail(String recipeId) async {
    try {
      emit(const RecipeDetailLoading());

      final futures = await Future.wait([
        _recipeService.getRecipeById(recipeId),
        _recipeService.getRecipeComments(
          recipeId: recipeId,
          page: 1,
          limit: _commentsPageSize,
        ),
      ]);

      final recipe = futures[0] as RecipeModel?;
      final comments = futures[1] as List<CommentModel>;

      if (recipe == null) {
        emit(const RecipeDetailError(message: 'Recipe not found'));
        return;
      }

      emit(RecipeDetailLoaded(
        recipe: recipe,
        comments: comments,
        hasMoreComments: comments.length >= _commentsPageSize,
        currentCommentPage: 1,
      ));
    } catch (e) {
      emit(RecipeDetailError(message: e.toString()));
    }
  }

  /// Load more comments
  Future<void> loadMoreComments() async {
    final currentState = state;
    if (currentState is! RecipeDetailLoaded || 
        !currentState.hasMoreComments || 
        currentState.isLoadingComments) {
      return;
    }

    try {
      emit(currentState.copyWith(isLoadingComments: true));

      final nextPage = currentState.currentCommentPage + 1;
      final newComments = await _recipeService.getRecipeComments(
        recipeId: currentState.recipe.id,
        page: nextPage,
        limit: _commentsPageSize,
      );

      final allComments = [...currentState.comments, ...newComments];

      emit(RecipeDetailLoaded(
        recipe: currentState.recipe,
        comments: allComments,
        hasMoreComments: newComments.length >= _commentsPageSize,
        currentCommentPage: nextPage,
        isLoadingComments: false,
      ));
    } catch (e) {
      emit(RecipeDetailError(message: e.toString()));
    }
  }

  /// Add a comment
  Future<void> addComment({
    required String content,
    double? rating,
    String? parentCommentId,
  }) async {
    final currentState = state;
    if (currentState is! RecipeDetailLoaded) {
      return;
    }

    try {
      final newComment = await _recipeService.addComment(
        recipeId: currentState.recipe.id,
        content: content,
        rating: rating,
        parentCommentId: parentCommentId,
      );

      List<CommentModel> updatedComments;
      
      if (parentCommentId != null) {
        // Add as a reply to existing comment
        updatedComments = currentState.comments.map((comment) {
          if (comment.id == parentCommentId) {
            return comment.copyWith(
              replies: [...comment.replies, newComment],
            );
          }
          return comment;
        }).toList();
      } else {
        // Add as a new top-level comment
        updatedComments = [newComment, ...currentState.comments];
      }

      // Update recipe comment count
      final updatedRecipe = currentState.recipe.copyWith(
        commentsCount: currentState.recipe.commentsCount + 1,
      );

      emit(RecipeDetailCommentAdded(
        recipe: updatedRecipe,
        comments: updatedComments,
        newComment: newComment,
      ));

      // Return to loaded state
      emit(RecipeDetailLoaded(
        recipe: updatedRecipe,
        comments: updatedComments,
        hasMoreComments: currentState.hasMoreComments,
        currentCommentPage: currentState.currentCommentPage,
      ));
    } catch (e) {
      emit(RecipeDetailError(message: e.toString()));
    }
  }

  /// Rate the recipe
  Future<void> rateRecipe(double rating) async {
    final currentState = state;
    if (currentState is! RecipeDetailLoaded) {
      return;
    }

    try {
      await _recipeService.rateRecipe(currentState.recipe.id, rating);

      // Optimistically update the recipe rating
      // In a real app, you might want to refetch the recipe to get the updated rating
      final updatedRecipe = currentState.recipe.copyWith(
        rating: ((currentState.recipe.rating * currentState.recipe.ratingsCount) + rating) / 
                (currentState.recipe.ratingsCount + 1),
        ratingsCount: currentState.recipe.ratingsCount + 1,
      );

      emit(RecipeDetailRatingUpdated(
        recipe: updatedRecipe,
        comments: currentState.comments,
      ));

      // Return to loaded state
      emit(RecipeDetailLoaded(
        recipe: updatedRecipe,
        comments: currentState.comments,
        hasMoreComments: currentState.hasMoreComments,
        currentCommentPage: currentState.currentCommentPage,
      ));
    } catch (e) {
      emit(RecipeDetailError(message: e.toString()));
    }
  }

  /// Refresh recipe details
  Future<void> refreshRecipeDetail() async {
    final currentState = state;
    if (currentState is RecipeDetailLoaded) {
      await loadRecipeDetail(currentState.recipe.id);
    }
  }
}
