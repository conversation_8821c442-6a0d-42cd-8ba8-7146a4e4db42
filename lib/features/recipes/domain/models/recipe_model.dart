import 'package:equatable/equatable.dart';

enum RecipeCategory {
  breakfast,
  lunch,
  dinner,
  snacks,
  desserts,
  beverages,
  traditional,
  modern,
}

enum DifficultyLevel {
  easy,
  medium,
  hard,
}

class RecipeModel extends Equatable {
  final String id;
  final String title;
  final String description;
  final List<String> imageUrls;
  final String instructions;
  final List<String> ingredients;
  final int preparationTimeMinutes;
  final int cookingTimeMinutes;
  final int servings;
  final RecipeCategory category;
  final DifficultyLevel difficulty;
  final String authorId;
  final String authorName;
  final String? authorImageUrl;
  final double rating;
  final int ratingsCount;
  final int commentsCount;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isSpecialToday;

  const RecipeModel({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrls,
    required this.instructions,
    required this.ingredients,
    required this.preparationTimeMinutes,
    required this.cookingTimeMinutes,
    required this.servings,
    required this.category,
    required this.difficulty,
    required this.authorId,
    required this.authorName,
    this.authorImageUrl,
    this.rating = 0.0,
    this.ratingsCount = 0,
    this.commentsCount = 0,
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isSpecialToday = false,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        imageUrls,
        instructions,
        ingredients,
        preparationTimeMinutes,
        cookingTimeMinutes,
        servings,
        category,
        difficulty,
        authorId,
        authorName,
        authorImageUrl,
        rating,
        ratingsCount,
        commentsCount,
        tags,
        createdAt,
        updatedAt,
        isSpecialToday,
      ];

  int get totalTimeMinutes => preparationTimeMinutes + cookingTimeMinutes;

  String get categoryDisplayName {
    switch (category) {
      case RecipeCategory.breakfast:
        return 'Breakfast';
      case RecipeCategory.lunch:
        return 'Lunch';
      case RecipeCategory.dinner:
        return 'Dinner';
      case RecipeCategory.snacks:
        return 'Snacks';
      case RecipeCategory.desserts:
        return 'Desserts';
      case RecipeCategory.beverages:
        return 'Beverages';
      case RecipeCategory.traditional:
        return 'Traditional';
      case RecipeCategory.modern:
        return 'Modern';
    }
  }

  String get difficultyDisplayName {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return 'Easy';
      case DifficultyLevel.medium:
        return 'Medium';
      case DifficultyLevel.hard:
        return 'Hard';
    }
  }

  RecipeModel copyWith({
    String? id,
    String? title,
    String? description,
    List<String>? imageUrls,
    String? instructions,
    List<String>? ingredients,
    int? preparationTimeMinutes,
    int? cookingTimeMinutes,
    int? servings,
    RecipeCategory? category,
    DifficultyLevel? difficulty,
    String? authorId,
    String? authorName,
    String? authorImageUrl,
    double? rating,
    int? ratingsCount,
    int? commentsCount,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSpecialToday,
  }) {
    return RecipeModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrls: imageUrls ?? this.imageUrls,
      instructions: instructions ?? this.instructions,
      ingredients: ingredients ?? this.ingredients,
      preparationTimeMinutes: preparationTimeMinutes ?? this.preparationTimeMinutes,
      cookingTimeMinutes: cookingTimeMinutes ?? this.cookingTimeMinutes,
      servings: servings ?? this.servings,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      authorImageUrl: authorImageUrl ?? this.authorImageUrl,
      rating: rating ?? this.rating,
      ratingsCount: ratingsCount ?? this.ratingsCount,
      commentsCount: commentsCount ?? this.commentsCount,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSpecialToday: isSpecialToday ?? this.isSpecialToday,
    );
  }

  factory RecipeModel.fromJson(Map<String, dynamic> json) {
    return RecipeModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      imageUrls: List<String>.from(json['image_urls'] as List? ?? []),
      instructions: json['instructions'] as String,
      ingredients: List<String>.from(json['ingredients'] as List? ?? []),
      preparationTimeMinutes: json['preparation_time_minutes'] as int,
      cookingTimeMinutes: json['cooking_time_minutes'] as int,
      servings: json['servings'] as int,
      category: RecipeCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => RecipeCategory.traditional,
      ),
      difficulty: DifficultyLevel.values.firstWhere(
        (e) => e.name == json['difficulty'],
        orElse: () => DifficultyLevel.medium,
      ),
      authorId: json['author_id'] as String,
      authorName: json['author_name'] as String,
      authorImageUrl: json['author_image_url'] as String?,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      ratingsCount: json['ratings_count'] as int? ?? 0,
      commentsCount: json['comments_count'] as int? ?? 0,
      tags: List<String>.from(json['tags'] as List? ?? []),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isSpecialToday: json['is_special_today'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image_urls': imageUrls,
      'instructions': instructions,
      'ingredients': ingredients,
      'preparation_time_minutes': preparationTimeMinutes,
      'cooking_time_minutes': cookingTimeMinutes,
      'servings': servings,
      'category': category.name,
      'difficulty': difficulty.name,
      'author_id': authorId,
      'author_name': authorName,
      'author_image_url': authorImageUrl,
      'rating': rating,
      'ratings_count': ratingsCount,
      'comments_count': commentsCount,
      'tags': tags,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_special_today': isSpecialToday,
    };
  }
}

class CreateRecipeRequest extends Equatable {
  final String title;
  final String description;
  final List<String> imageUrls;
  final String instructions;
  final List<String> ingredients;
  final int preparationTimeMinutes;
  final int cookingTimeMinutes;
  final int servings;
  final RecipeCategory category;
  final DifficultyLevel difficulty;
  final List<String> tags;

  const CreateRecipeRequest({
    required this.title,
    required this.description,
    required this.imageUrls,
    required this.instructions,
    required this.ingredients,
    required this.preparationTimeMinutes,
    required this.cookingTimeMinutes,
    required this.servings,
    required this.category,
    required this.difficulty,
    this.tags = const [],
  });

  @override
  List<Object?> get props => [
        title,
        description,
        imageUrls,
        instructions,
        ingredients,
        preparationTimeMinutes,
        cookingTimeMinutes,
        servings,
        category,
        difficulty,
        tags,
      ];

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'image_urls': imageUrls,
      'instructions': instructions,
      'ingredients': ingredients,
      'preparation_time_minutes': preparationTimeMinutes,
      'cooking_time_minutes': cookingTimeMinutes,
      'servings': servings,
      'category': category.name,
      'difficulty': difficulty.name,
      'tags': tags,
    };
  }
}
