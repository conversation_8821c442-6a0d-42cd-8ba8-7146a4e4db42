import 'package:equatable/equatable.dart';

class CommentModel extends Equatable {
  final String id;
  final String recipeId;
  final String authorId;
  final String authorName;
  final String? authorImageUrl;
  final String content;
  final double? rating;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? parentCommentId; // For replies
  final List<CommentModel> replies;

  const CommentModel({
    required this.id,
    required this.recipeId,
    required this.authorId,
    required this.authorName,
    this.authorImageUrl,
    required this.content,
    this.rating,
    required this.createdAt,
    required this.updatedAt,
    this.parentCommentId,
    this.replies = const [],
  });

  @override
  List<Object?> get props => [
        id,
        recipeId,
        authorId,
        authorName,
        authorImageUrl,
        content,
        rating,
        createdAt,
        updatedAt,
        parentCommentId,
        replies,
      ];

  bool get isReply => parentCommentId != null;
  bool get hasRating => rating != null;

  CommentModel copyWith({
    String? id,
    String? recipeId,
    String? authorId,
    String? authorName,
    String? authorImageUrl,
    String? content,
    double? rating,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? parentCommentId,
    List<CommentModel>? replies,
  }) {
    return CommentModel(
      id: id ?? this.id,
      recipeId: recipeId ?? this.recipeId,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      authorImageUrl: authorImageUrl ?? this.authorImageUrl,
      content: content ?? this.content,
      rating: rating ?? this.rating,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      parentCommentId: parentCommentId ?? this.parentCommentId,
      replies: replies ?? this.replies,
    );
  }

  factory CommentModel.fromJson(Map<String, dynamic> json) {
    return CommentModel(
      id: json['id'] as String,
      recipeId: json['recipe_id'] as String,
      authorId: json['author_id'] as String,
      authorName: json['author_name'] as String,
      authorImageUrl: json['author_image_url'] as String?,
      content: json['content'] as String,
      rating: (json['rating'] as num?)?.toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      parentCommentId: json['parent_comment_id'] as String?,
      replies: (json['replies'] as List?)
              ?.map((reply) => CommentModel.fromJson(reply as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'recipe_id': recipeId,
      'author_id': authorId,
      'author_name': authorName,
      'author_image_url': authorImageUrl,
      'content': content,
      'rating': rating,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'parent_comment_id': parentCommentId,
      'replies': replies.map((reply) => reply.toJson()).toList(),
    };
  }
}

class CreateCommentRequest extends Equatable {
  final String recipeId;
  final String content;
  final double? rating;
  final String? parentCommentId;

  const CreateCommentRequest({
    required this.recipeId,
    required this.content,
    this.rating,
    this.parentCommentId,
  });

  @override
  List<Object?> get props => [recipeId, content, rating, parentCommentId];

  Map<String, dynamic> toJson() {
    return {
      'recipe_id': recipeId,
      'content': content,
      'rating': rating,
      'parent_comment_id': parentCommentId,
    };
  }
}
