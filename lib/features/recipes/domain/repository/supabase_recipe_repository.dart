import 'dart:io';
import 'package:injectable/injectable.dart';
import '../models/recipe_model.dart';
import '../models/comment_model.dart';
import 'recipe_repository.dart';
import '../../../../core/network/supabase_client.dart';

@Injectable(as: RecipeRepository)
class SupabaseRecipeRepository implements RecipeRepository {
  final SupabaseNetworkClient _networkClient;

  SupabaseRecipeRepository(this._networkClient);

  @override
  Future<List<RecipeModel>> getRecipes({
    int page = 1,
    int limit = 10,
    RecipeCategory? category,
    String? searchQuery,
  }) async {
    try {
      var query = _networkClient.client
          .from('recipes')
          .select('*');

      if (category != null) {
        query = query.eq('category', category.name);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('title.ilike.%$searchQuery%,description.ilike.%$searchQuery%');
      }

      final response = await query
          .order('created_at', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      return (response as List)
          .map((json) => RecipeModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch recipes: $e');
    }
  }

  @override
  Future<List<RecipeModel>> getTodaysSpecialRecipes() async {
    try {
      final response = await _networkClient.client
          .from('recipes')
          .select('*')
          .eq('is_special_today', true)
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => RecipeModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch today\'s special recipes: $e');
    }
  }

  @override
  Future<RecipeModel?> getRecipeById(String recipeId) async {
    try {
      final response = await _networkClient.client
          .from('recipes')
          .select('*')
          .eq('id', recipeId)
          .single();

      return RecipeModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<CommentModel>> getRecipeComments({
    required String recipeId,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _networkClient.client
          .from('recipe_comments')
          .select('*')
          .eq('recipe_id', recipeId)
          .isFilter('parent_comment_id', null)
          .order('created_at', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      // Get replies separately
      final repliesResponse = await _networkClient.client
          .from('recipe_comments')
          .select('*')
          .eq('recipe_id', recipeId)
          .not('parent_comment_id', 'is', null);

      final repliesMap = <String, List<CommentModel>>{};
      for (final reply in repliesResponse as List) {
        final parentId = reply['parent_comment_id'] as String;
        if (!repliesMap.containsKey(parentId)) {
          repliesMap[parentId] = [];
        }
        repliesMap[parentId]!.add(CommentModel.fromJson(reply));
      }

      return (response as List)
          .map((json) {
            final comment = CommentModel.fromJson(json);
            final replies = repliesMap[comment.id] ?? [];
            return comment.copyWith(replies: replies);
          })
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch comments: $e');
    }
  }

  @override
  Future<CommentModel> addComment(CreateCommentRequest request) async {
    try {
      final user = _networkClient.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final commentData = {
        ...request.toJson(),
        'author_id': user.id,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await _networkClient.client
          .from('recipe_comments')
          .insert(commentData)
          .select('*')
          .single();

      // Update recipe comments count
      await _networkClient.client.rpc('increment_recipe_comments_count',
          params: {'recipe_id': request.recipeId});

      return CommentModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to add comment: $e');
    }
  }

  @override
  Future<void> updateRecipeRating(String recipeId, double rating) async {
    try {
      await _networkClient.client.rpc('update_recipe_rating', params: {
        'recipe_id': recipeId,
        'new_rating': rating,
      });
    } catch (e) {
      throw Exception('Failed to update rating: $e');
    }
  }

  @override
  Future<List<RecipeModel>> searchRecipes({
    required String query,
    int page = 1,
    int limit = 10,
  }) async {
    return getRecipes(
      page: page,
      limit: limit,
      searchQuery: query,
    );
  }

  @override
  Future<List<RecipeModel>> getRecipesByCategory({
    required RecipeCategory category,
    int page = 1,
    int limit = 10,
  }) async {
    return getRecipes(
      page: page,
      limit: limit,
      category: category,
    );
  }

  @override
  Future<List<RecipeModel>> getFeaturedRecipes() async {
    try {
      final response = await _networkClient.client
          .from('recipes')
          .select('*')
          .gte('rating', 4.0)
          .order('rating', ascending: false)
          .limit(5);

      return (response as List)
          .map((json) => RecipeModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch featured recipes: $e');
    }
  }

  @override
  Future<String> uploadRecipeImage(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        throw Exception('Image file does not exist');
      }

      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = imagePath.split('.').last;
      final fileName = 'recipe_$timestamp.$extension';

      // Upload to Supabase storage
      await _networkClient.client.storage
          .from('recipe-images')
          .upload(fileName, file);

      // Get public URL
      final imageUrl = _networkClient.client.storage
          .from('recipe-images')
          .getPublicUrl(fileName);

      return imageUrl;
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }

  @override
  Future<RecipeModel> createRecipe(CreateRecipeRequest request) async {
    try {
      final user = _networkClient.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final recipeData = {
        ...request.toJson(),
        'author_id': user.id,
        'author_name': user.userMetadata?['full_name'] ?? 'Anonymous',
        'author_image_url': user.userMetadata?['avatar_url'],
        'rating': 0.0,
        'ratings_count': 0,
        'comments_count': 0,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'is_special_today': false,
      };

      final response = await _networkClient.client
          .from('recipes')
          .insert(recipeData)
          .select('*')
          .single();

      return RecipeModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create recipe: $e');
    }
  }
}
