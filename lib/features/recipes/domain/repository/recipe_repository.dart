import '../models/recipe_model.dart';
import '../models/comment_model.dart';

abstract class RecipeRepository {
  /// Get paginated list of recipes
  Future<List<RecipeModel>> getRecipes({
    int page = 1,
    int limit = 10,
    RecipeCategory? category,
    String? searchQuery,
  });

  /// Get today's special recipes
  Future<List<RecipeModel>> getTodaysSpecialRecipes();

  /// Get recipe by ID
  Future<RecipeModel?> getRecipeById(String recipeId);

  /// Get comments for a recipe
  Future<List<CommentModel>> getRecipeComments({
    required String recipeId,
    int page = 1,
    int limit = 20,
  });

  /// Add a comment to a recipe
  Future<CommentModel> addComment(CreateCommentRequest request);

  /// Update recipe rating
  Future<void> updateRecipeRating(String recipeId, double rating);

  /// Search recipes
  Future<List<RecipeModel>> searchRecipes({
    required String query,
    int page = 1,
    int limit = 10,
  });

  /// Get recipes by category
  Future<List<RecipeModel>> getRecipesByCategory({
    required RecipeCategory category,
    int page = 1,
    int limit = 10,
  });

  /// Get featured recipes
  Future<List<RecipeModel>> getFeaturedRecipes();

  /// Upload recipe image
  Future<String> uploadRecipeImage(String imagePath);

  /// Create a new recipe
  Future<RecipeModel> createRecipe(CreateRecipeRequest request);
}
