import 'package:injectable/injectable.dart';
import '../models/recipe_model.dart';
import '../models/comment_model.dart';
import '../repository/recipe_repository.dart';

@injectable
class RecipeService {
  final RecipeRepository _repository;

  RecipeService(this._repository);

  /// Get paginated recipes with optional filtering
  Future<List<RecipeModel>> getRecipes({
    int page = 1,
    int limit = 10,
    RecipeCategory? category,
    String? searchQuery,
  }) async {
    return await _repository.getRecipes(
      page: page,
      limit: limit,
      category: category,
      searchQuery: searchQuery,
    );
  }

  /// Get today's special recipes
  Future<List<RecipeModel>> getTodaysSpecialRecipes() async {
    return await _repository.getTodaysSpecialRecipes();
  }

  /// Get recipe details by ID
  Future<RecipeModel?> getRecipeById(String recipeId) async {
    return await _repository.getRecipeById(recipeId);
  }

  /// Get comments for a recipe
  Future<List<CommentModel>> getRecipeComments({
    required String recipeId,
    int page = 1,
    int limit = 20,
  }) async {
    return await _repository.getRecipeComments(
      recipeId: recipeId,
      page: page,
      limit: limit,
    );
  }

  /// Add a comment to a recipe
  Future<CommentModel> addComment({
    required String recipeId,
    required String content,
    double? rating,
    String? parentCommentId,
  }) async {
    final request = CreateCommentRequest(
      recipeId: recipeId,
      content: content,
      rating: rating,
      parentCommentId: parentCommentId,
    );

    return await _repository.addComment(request);
  }

  /// Rate a recipe
  Future<void> rateRecipe(String recipeId, double rating) async {
    if (rating < 1.0 || rating > 5.0) {
      throw ArgumentError('Rating must be between 1.0 and 5.0');
    }
    
    await _repository.updateRecipeRating(recipeId, rating);
  }

  /// Search recipes
  Future<List<RecipeModel>> searchRecipes({
    required String query,
    int page = 1,
    int limit = 10,
  }) async {
    if (query.trim().isEmpty) {
      return [];
    }

    return await _repository.searchRecipes(
      query: query.trim(),
      page: page,
      limit: limit,
    );
  }

  /// Get recipes by category
  Future<List<RecipeModel>> getRecipesByCategory({
    required RecipeCategory category,
    int page = 1,
    int limit = 10,
  }) async {
    return await _repository.getRecipesByCategory(
      category: category,
      page: page,
      limit: limit,
    );
  }

  /// Get featured recipes
  Future<List<RecipeModel>> getFeaturedRecipes() async {
    return await _repository.getFeaturedRecipes();
  }

  /// Upload recipe image
  Future<String> uploadRecipeImage(String imagePath) async {
    return await _repository.uploadRecipeImage(imagePath);
  }

  /// Get recipe categories for filtering
  List<RecipeCategory> getRecipeCategories() {
    return RecipeCategory.values;
  }

  /// Get difficulty levels
  List<DifficultyLevel> getDifficultyLevels() {
    return DifficultyLevel.values;
  }

  /// Format cooking time for display
  String formatCookingTime(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${remainingMinutes}m';
      }
    }
  }

  /// Get recipe difficulty color
  String getDifficultyColor(DifficultyLevel difficulty) {
    switch (difficulty) {
      case DifficultyLevel.easy:
        return '#4CAF50'; // Green
      case DifficultyLevel.medium:
        return '#FF9800'; // Orange
      case DifficultyLevel.hard:
        return '#F44336'; // Red
    }
  }

  /// Check if recipe is suitable for beginners
  bool isBeginnerFriendly(RecipeModel recipe) {
    return recipe.difficulty == DifficultyLevel.easy && 
           recipe.totalTimeMinutes <= 60;
  }

  /// Get recipe complexity score (1-10)
  int getComplexityScore(RecipeModel recipe) {
    int score = 1;
    
    // Add points for difficulty
    switch (recipe.difficulty) {
      case DifficultyLevel.easy:
        score += 1;
        break;
      case DifficultyLevel.medium:
        score += 3;
        break;
      case DifficultyLevel.hard:
        score += 5;
        break;
    }
    
    // Add points for cooking time
    if (recipe.totalTimeMinutes > 120) {
      score += 3;
    } else if (recipe.totalTimeMinutes > 60) {
      score += 2;
    } else {
      score += 1;
    }
    
    // Add points for number of ingredients
    if (recipe.ingredients.length > 15) {
      score += 2;
    } else if (recipe.ingredients.length > 10) {
      score += 1;
    }
    
    return score.clamp(1, 10);
  }

  /// Create a new recipe
  Future<RecipeModel> createRecipe(CreateRecipeRequest request) async {
    return await _repository.createRecipe(request);
  }
}
