import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Helper class to upload recipe images to Supabase storage
class RecipeImageUploadHelper {
  static final _supabase = Supabase.instance.client;
  
  /// Upload a single image file to Supabase storage
  static Future<String> uploadImage(File imageFile, String fileName) async {
    try {
      // Upload the file to the recipe-images bucket
      await _supabase.storage
          .from('recipe-images')
          .upload(fileName, imageFile);
      
      // Get the public URL
      final imageUrl = _supabase.storage
          .from('recipe-images')
          .getPublicUrl(fileName);
      
      return imageUrl;
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }
  
  /// Upload multiple images for a recipe
  static Future<List<String>> uploadRecipeImages(
    List<File> imageFiles, 
    String recipeId
  ) async {
    final imageUrls = <String>[];
    
    for (int i = 0; i < imageFiles.length; i++) {
      final fileName = '${recipeId}_${i + 1}.jpg';
      final imageUrl = await uploadImage(imageFiles[i], fileName);
      imageUrls.add(imageUrl);
    }
    
    return imageUrls;
  }
  
  /// Update recipe with new image URLs
  static Future<void> updateRecipeImages(
    String recipeId, 
    List<String> imageUrls
  ) async {
    try {
      await _supabase
          .from('recipes')
          .update({'image_urls': imageUrls})
          .eq('id', recipeId);
    } catch (e) {
      throw Exception('Failed to update recipe images: $e');
    }
  }
  
  /// Get Supabase storage URL for a file
  static String getStorageUrl(String fileName) {
    return _supabase.storage
        .from('recipe-images')
        .getPublicUrl(fileName);
  }
  
  /// List all files in the recipe-images bucket
  static Future<List<String>> listStorageFiles() async {
    try {
      final response = await _supabase.storage
          .from('recipe-images')
          .list();
      
      return response.map((file) => file.name).toList();
    } catch (e) {
      throw Exception('Failed to list storage files: $e');
    }
  }
  
  /// Delete an image from storage
  static Future<void> deleteImage(String fileName) async {
    try {
      await _supabase.storage
          .from('recipe-images')
          .remove([fileName]);
    } catch (e) {
      throw Exception('Failed to delete image: $e');
    }
  }
}

/// Sample food image URLs that you can use temporarily
class SampleFoodImages {
  static const Map<String, List<String>> recipeImages = {
    'Kerala Fish Curry': [
      'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
    ],
    'Chicken Biriyani': [
      'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=800&h=600&fit=crop',
    ],
    'Masala Dosa': [
      'https://images.unsplash.com/photo-1567188040759-fb8a883dc6d8?w=800&h=600&fit=crop',
    ],
    'Butter Chicken': [
      'https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=800&h=600&fit=crop',
    ],
    'Mango Lassi': [
      'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=800&h=600&fit=crop',
    ],
    'Chocolate Chip Cookies': [
      'https://images.unsplash.com/photo-1499636136210-6f4ee915583e?w=800&h=600&fit=crop',
    ],
    'Appam with Stew': [
      'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=800&h=600&fit=crop',
    ],
    'Puttu and Kadala Curry': [
      'https://images.unsplash.com/photo-1606491956689-2ea866880c84?w=800&h=600&fit=crop',
    ],
    'Sambar Rice': [
      'https://images.unsplash.com/photo-1585937421612-70a008356fbe?w=800&h=600&fit=crop',
    ],
    'Avocado Toast': [
      'https://images.unsplash.com/photo-1541519227354-08fa5d50c44d?w=800&h=600&fit=crop',
    ],
    'Payasam': [
      'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=800&h=600&fit=crop',
    ],
    'Quinoa Salad Bowl': [
      'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=800&h=600&fit=crop',
    ],
    'Masala Chai': [
      'https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=800&h=600&fit=crop',
    ],
    'Fish Molee': [
      'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
    ],
  };
  
  /// Update all recipes with sample food images
  static Future<void> updateAllRecipesWithSampleImages() async {
    final supabase = Supabase.instance.client;
    
    for (final entry in recipeImages.entries) {
      final recipeName = entry.key;
      final imageUrls = entry.value;
      
      try {
        await supabase
            .from('recipes')
            .update({'image_urls': imageUrls})
            .eq('title', recipeName);
        
        print('✅ Updated $recipeName with ${imageUrls.length} images');
      } catch (e) {
        print('❌ Error updating $recipeName: $e');
      }
    }
  }
}
