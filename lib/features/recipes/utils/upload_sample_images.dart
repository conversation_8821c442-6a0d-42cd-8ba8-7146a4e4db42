import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';

/// Utility to upload sample recipe images to Supabase storage
class UploadSampleImages {
  static final _supabase = Supabase.instance.client;
  
  /// Sample food image URLs from Unsplash
  static const Map<String, String> sampleImageUrls = {
    'kerala-fish-curry.jpg': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
    'chicken-biryani.jpg': 'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=800&h=600&fit=crop',
    'masala-dosa.jpg': 'https://images.unsplash.com/photo-1567188040759-fb8a883dc6d8?w=800&h=600&fit=crop',
    'butter-chicken.jpg': 'https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=800&h=600&fit=crop',
    'mango-lassi.jpg': 'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=800&h=600&fit=crop',
    'chocolate-cookies.jpg': 'https://images.unsplash.com/photo-1499636136210-6f4ee915583e?w=800&h=600&fit=crop',
    'appam-stew.jpg': 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=800&h=600&fit=crop',
    'puttu-kadala.jpg': 'https://images.unsplash.com/photo-1606491956689-2ea866880c84?w=800&h=600&fit=crop',
    'sambar-rice.jpg': 'https://images.unsplash.com/photo-1585937421612-70a008356fbe?w=800&h=600&fit=crop',
    'avocado-toast.jpg': 'https://images.unsplash.com/photo-1541519227354-08fa5d50c44d?w=800&h=600&fit=crop',
    'payasam.jpg': 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=800&h=600&fit=crop',
    'quinoa-salad.jpg': 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=800&h=600&fit=crop',
    'masala-chai.jpg': 'https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=800&h=600&fit=crop',
    'fish-molee.jpg': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
  };

  /// Recipe title to filename mapping
  static const Map<String, String> recipeToFilename = {
    'Kerala Fish Curry': 'kerala-fish-curry.jpg',
    'Chicken Biriyani': 'chicken-biryani.jpg',
    'Masala Dosa': 'masala-dosa.jpg',
    'Butter Chicken': 'butter-chicken.jpg',
    'Mango Lassi': 'mango-lassi.jpg',
    'Chocolate Chip Cookies': 'chocolate-cookies.jpg',
    'Appam with Stew': 'appam-stew.jpg',
    'Puttu and Kadala Curry': 'puttu-kadala.jpg',
    'Sambar Rice': 'sambar-rice.jpg',
    'Avocado Toast': 'avocado-toast.jpg',
    'Payasam': 'payasam.jpg',
    'Quinoa Salad Bowl': 'quinoa-salad.jpg',
    'Masala Chai': 'masala-chai.jpg',
    'Fish Molee': 'fish-molee.jpg',
  };

  /// Download image from URL
  static Future<Uint8List> downloadImage(String url) async {
    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      return response.bodyBytes;
    } else {
      throw Exception('Failed to download image: ${response.statusCode}');
    }
  }

  /// Upload image to Supabase storage
  static Future<String> uploadImageToStorage(String fileName, Uint8List imageBytes) async {
    try {
      // Upload the file to the recipe-images bucket
      await _supabase.storage
          .from('recipe-images')
          .uploadBinary(fileName, imageBytes, fileOptions: const FileOptions(
            upsert: true, // Allow overwriting existing files
          ));

      // Get the public URL
      final imageUrl = _supabase.storage
          .from('recipe-images')
          .getPublicUrl(fileName);

      return imageUrl;
    } catch (e) {
      throw Exception('Failed to upload image to storage: $e');
    }
  }

  /// Upload all sample images to Supabase storage
  static Future<Map<String, String>> uploadAllSampleImages() async {
    final uploadedImages = <String, String>{};
    
    print('🚀 Starting to upload sample images to Supabase storage...');
    
    for (final entry in sampleImageUrls.entries) {
      final fileName = entry.key;
      final imageUrl = entry.value;
      
      try {
        print('📥 Downloading $fileName from $imageUrl');
        final imageBytes = await downloadImage(imageUrl);
        
        print('📤 Uploading $fileName to Supabase storage');
        final storageUrl = await uploadImageToStorage(fileName, imageBytes);
        
        uploadedImages[fileName] = storageUrl;
        print('✅ Successfully uploaded $fileName');
        
        // Add a small delay to avoid rate limiting
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        print('❌ Error uploading $fileName: $e');
      }
    }
    
    print('🎉 Finished uploading ${uploadedImages.length} images');
    return uploadedImages;
  }

  /// Update recipes with uploaded image URLs
  static Future<void> updateRecipesWithStorageUrls(Map<String, String> uploadedImages) async {
    print('🔄 Updating recipes with storage URLs...');
    
    for (final entry in recipeToFilename.entries) {
      final recipeTitle = entry.key;
      final fileName = entry.value;
      
      if (uploadedImages.containsKey(fileName)) {
        final imageUrl = uploadedImages[fileName]!;
        
        try {
          await _supabase
              .from('recipes')
              .update({'image_urls': [imageUrl]})
              .eq('title', recipeTitle);
          
          print('✅ Updated $recipeTitle with storage URL');
        } catch (e) {
          print('❌ Error updating $recipeTitle: $e');
        }
      }
    }
    
    print('🎉 Finished updating recipes with storage URLs');
  }

  /// Complete process: upload images and update recipes
  static Future<void> uploadAndUpdateRecipes() async {
    try {
      // Upload all sample images
      final uploadedImages = await uploadAllSampleImages();
      
      if (uploadedImages.isNotEmpty) {
        // Update recipes with the new URLs
        await updateRecipesWithStorageUrls(uploadedImages);
        
        print('🎊 All done! Recipes now have proper Supabase storage URLs');
      } else {
        print('❌ No images were uploaded successfully');
      }
    } catch (e) {
      print('💥 Error in upload process: $e');
    }
  }

  /// Check if recipe-images bucket exists and is accessible
  static Future<bool> checkStorageBucket() async {
    try {
      await _supabase.storage.from('recipe-images').list();
      print('✅ recipe-images bucket is accessible');
      return true;
    } catch (e) {
      print('❌ Error accessing recipe-images bucket: $e');
      return false;
    }
  }
}
