import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../widgets/memories_widget.dart';

class MemoriesScreen extends StatelessWidget {
  const MemoriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7FAFC),
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.photo_library,
                          color: Color(0xFF0D141C),
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Memories',
                        style: GoogleFonts.plusJakartaSans(
                          fontWeight: FontWeight.w700,
                          fontSize: 18,
                          color: const Color(0xFF0D141C),
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    onPressed: () {
                      _showComingSoon(context);
                    },
                    icon: const Icon(
                      Icons.add_a_photo,
                      color: Color(0xFF0D141C),
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
            
            // Memories Content
            const Expanded(
              child: MemoriesWidget(),
            ),
          ],
        ),
      ),
    );
  }

  void _showComingSoon(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Upload Photo - Coming Soon!',
          style: GoogleFonts.plusJakartaSans(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        backgroundColor: const Color(0xFF0D80F2),
      ),
    );
  }
}
