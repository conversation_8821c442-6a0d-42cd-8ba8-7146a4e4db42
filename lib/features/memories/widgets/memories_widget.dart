import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class MemoriesWidget extends StatefulWidget {
  const MemoriesWidget({super.key});

  @override
  State<MemoriesWidget> createState() => _MemoriesWidgetState();
}

class _MemoriesWidgetState extends State<MemoriesWidget> {
  String selectedCategory = 'All';

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Categories
          _buildCategories(),
          
          // Photo Grid
          _buildPhotoGrid(),
          
          // Bottom spacing for navigation
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildCategories() {
    final categories = ['All', 'Family', 'Events', 'Festivals', 'Birthdays'];
    
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 12),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = category == selectedCategory;
          
          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: GestureDetector(
              onTap: () => setState(() => selectedCategory = category),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? const Color(0xFF0D80F2) : Colors.white,
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: isSelected ? const Color(0xFF0D80F2) : const Color(0xFFE8EDF5),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  category,
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: isSelected ? Colors.white : const Color(0xFF4A739C),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPhotoGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Recent Photos Section
        _buildPhotoSection('Recent Photos', _getRecentPhotos()),
        
        // Onam Festival Section
        _buildPhotoSection('Onam Festival 2024', _getOnamPhotos()),
        
        // Family Gatherings Section
        _buildPhotoSection('Family Gatherings', _getFamilyPhotos()),
      ],
    );
  }

  Widget _buildPhotoSection(String title, List<Map<String, dynamic>> photos) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w700,
                  fontSize: 18,
                  color: const Color(0xFF0D141C),
                ),
              ),
              Text(
                '${photos.length} photos',
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: const Color(0xFF4A739C),
                ),
              ),
            ],
          ),
        ),
        
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: photos.length,
            itemBuilder: (context, index) {
              final photo = photos[index];
              return Container(
                width: 100,
                margin: const EdgeInsets.only(right: 12),
                child: GestureDetector(
                  onTap: () => _showPhotoDetail(context, photo),
                  child: Container(
                    decoration: BoxDecoration(
                      color: photo['color'] as Color,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.photo,
                          size: 40,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          photo['title'] as String,
                          style: GoogleFonts.plusJakartaSans(
                            fontWeight: FontWeight.w500,
                            fontSize: 10,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  List<Map<String, dynamic>> _getRecentPhotos() {
    return [
      {
        'title': 'Family Dinner',
        'date': '2 days ago',
        'color': const Color(0xFF4A739C),
      },
      {
        'title': 'Birthday Party',
        'date': '1 week ago',
        'color': const Color(0xFF0D80F2),
      },
      {
        'title': 'Weekend Trip',
        'date': '2 weeks ago',
        'color': const Color(0xFF6B73FF),
      },
      {
        'title': 'Cooking Together',
        'date': '3 weeks ago',
        'color': const Color(0xFF9C4A73),
      },
    ];
  }

  List<Map<String, dynamic>> _getOnamPhotos() {
    return [
      {
        'title': 'Pookalam',
        'date': 'Sep 10, 2024',
        'color': const Color(0xFFFF6B6B),
      },
      {
        'title': 'Sadhya',
        'date': 'Sep 10, 2024',
        'color': const Color(0xFF4ECDC4),
      },
      {
        'title': 'Traditional Dance',
        'date': 'Sep 10, 2024',
        'color': const Color(0xFF45B7D1),
      },
      {
        'title': 'Family Photo',
        'date': 'Sep 10, 2024',
        'color': const Color(0xFF96CEB4),
      },
      {
        'title': 'Decorations',
        'date': 'Sep 10, 2024',
        'color': const Color(0xFFFECEA8),
      },
    ];
  }

  List<Map<String, dynamic>> _getFamilyPhotos() {
    return [
      {
        'title': 'Grandparents Visit',
        'date': 'Aug 15, 2024',
        'color': const Color(0xFF8E44AD),
      },
      {
        'title': 'Cousin Wedding',
        'date': 'Jul 20, 2024',
        'color': const Color(0xFFE74C3C),
      },
      {
        'title': 'Beach Vacation',
        'date': 'Jun 5, 2024',
        'color': const Color(0xFF3498DB),
      },
    ];
  }

  void _showPhotoDetail(BuildContext context, Map<String, dynamic> photo) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            width: 300,
            height: 400,
            decoration: BoxDecoration(
              color: photo['color'] as Color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.photo,
                  size: 100,
                  color: Colors.white,
                ),
                const SizedBox(height: 16),
                Text(
                  photo['title'] as String,
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w700,
                    fontSize: 20,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  photo['date'] as String,
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: photo['color'] as Color,
                  ),
                  child: Text(
                    'Close',
                    style: GoogleFonts.plusJakartaSans(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
