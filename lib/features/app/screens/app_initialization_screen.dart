import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../auth/logic/auth/auth_cubit.dart';
import '../../auth/logic/auth/auth_state.dart';
import '../../auth/domain/repository/auth_repository.dart';
import '../../welcome/screens/welcome_screen.dart';
import '../../dashboard/screens/dashboard_screen.dart';
import '../../../core/di/injection.dart';

class AppInitializationScreen extends StatelessWidget {
  const AppInitializationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AuthCubit(getIt<AuthRepository>())..checkAuthStatus(),
      child: const AppInitializationView(),
    );
  }
}

class AppInitializationView extends StatelessWidget {
  const AppInitializationView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is AuthSuccess) {
          // User is authenticated, navigate to dashboard
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => DashboardScreen(user: state.user),
            ),
          );
        } else if (state is AuthUnauthenticated || state is AuthError) {
          // User is not authenticated, navigate to welcome screen
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const WelcomeScreen(),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF7FAFC),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo or Icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFFDBE8F2),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.family_restroom,
                  size: 40,
                  color: Color(0xFF0D141C),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // App Name
              Text(
                'Kerala Family App',
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w700,
                  fontSize: 24,
                  color: const Color(0xFF0D141C),
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Loading Indicator
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D80F2)),
              ),
              
              const SizedBox(height: 16),
              
              // Loading Text
              Text(
                'Initializing...',
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 16,
                  color: const Color(0xFF4A739C),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
