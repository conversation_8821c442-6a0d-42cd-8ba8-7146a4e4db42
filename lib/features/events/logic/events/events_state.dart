import 'package:equatable/equatable.dart';
import '../../domain/models/event_model.dart';

abstract class EventsState extends Equatable {
  const EventsState();

  @override
  List<Object?> get props => [];
}

class EventsInitial extends EventsState {
  const EventsInitial();
}

class EventsLoading extends EventsState {
  const EventsLoading();
}

class EventsLoaded extends EventsState {
  final List<EventModel> upcomingEvents;
  final List<EventModel> pastEvents;
  final List<EventModel> birthdayEvents;
  final List<EventModel> todaysEvents;
  final Map<String, int> statistics;

  const EventsLoaded({
    required this.upcomingEvents,
    required this.pastEvents,
    required this.birthdayEvents,
    required this.todaysEvents,
    required this.statistics,
  });

  @override
  List<Object?> get props => [
        upcomingEvents,
        pastEvents,
        birthdayEvents,
        todaysEvents,
        statistics,
      ];

  EventsLoaded copyWith({
    List<EventModel>? upcomingEvents,
    List<EventModel>? pastEvents,
    List<EventModel>? birthdayEvents,
    List<EventModel>? todaysEvents,
    Map<String, int>? statistics,
  }) {
    return EventsLoaded(
      upcomingEvents: upcomingEvents ?? this.upcomingEvents,
      pastEvents: pastEvents ?? this.pastEvents,
      birthdayEvents: birthdayEvents ?? this.birthdayEvents,
      todaysEvents: todaysEvents ?? this.todaysEvents,
      statistics: statistics ?? this.statistics,
    );
  }
}

class EventsError extends EventsState {
  final String message;

  const EventsError({required this.message});

  @override
  List<Object?> get props => [message];
}

class EventsRefreshing extends EventsState {
  final List<EventModel> upcomingEvents;
  final List<EventModel> pastEvents;
  final List<EventModel> birthdayEvents;
  final List<EventModel> todaysEvents;
  final Map<String, int> statistics;

  const EventsRefreshing({
    required this.upcomingEvents,
    required this.pastEvents,
    required this.birthdayEvents,
    required this.todaysEvents,
    required this.statistics,
  });

  @override
  List<Object?> get props => [
        upcomingEvents,
        pastEvents,
        birthdayEvents,
        todaysEvents,
        statistics,
      ];
}

// Event creation states
abstract class EventCreationState extends Equatable {
  const EventCreationState();

  @override
  List<Object?> get props => [];
}

class EventCreationInitial extends EventCreationState {
  const EventCreationInitial();
}

class EventCreationLoading extends EventCreationState {
  const EventCreationLoading();
}

class EventCreationSuccess extends EventCreationState {
  final EventModel event;

  const EventCreationSuccess({required this.event});

  @override
  List<Object?> get props => [event];
}

class EventCreationError extends EventCreationState {
  final String message;

  const EventCreationError({required this.message});

  @override
  List<Object?> get props => [message];
}

// Event detail states
abstract class EventDetailState extends Equatable {
  const EventDetailState();

  @override
  List<Object?> get props => [];
}

class EventDetailInitial extends EventDetailState {
  const EventDetailInitial();
}

class EventDetailLoading extends EventDetailState {
  const EventDetailLoading();
}

class EventDetailLoaded extends EventDetailState {
  final EventModel event;

  const EventDetailLoaded({required this.event});

  @override
  List<Object?> get props => [event];
}

class EventDetailError extends EventDetailState {
  final String message;

  const EventDetailError({required this.message});

  @override
  List<Object?> get props => [message];
}

class EventDetailUpdating extends EventDetailState {
  final EventModel event;

  const EventDetailUpdating({required this.event});

  @override
  List<Object?> get props => [event];
}
