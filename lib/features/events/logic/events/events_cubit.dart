import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/services/event_service.dart';
import '../../domain/models/event_model.dart';
import 'events_state.dart';

class EventsCubit extends Cubit<EventsState> {
  final EventService _eventService;

  EventsCubit(this._eventService) : super(const EventsInitial());

  /// Load all events data
  Future<void> loadEvents() async {
    try {
      emit(const EventsLoading());

      final upcomingEvents = await _eventService.getUpcomingEvents();
      final pastEvents = await _eventService.getPastEvents();
      final birthdayEvents = await _eventService.getBirthdayEvents();
      final todaysEvents = await _eventService.getTodaysEvents();
      final statistics = await _eventService.getEventStatistics();

      emit(EventsLoaded(
        upcomingEvents: upcomingEvents,
        pastEvents: pastEvents,
        birthdayEvents: birthdayEvents.where((event) => event.isUpcoming).toList(),
        todaysEvents: todaysEvents,
        statistics: statistics,
      ));
    } catch (e) {
      emit(EventsError(message: e.toString()));
    }
  }

  /// Refresh events data
  Future<void> refreshEvents() async {
    final currentState = state;
    if (currentState is EventsLoaded) {
      try {
        emit(EventsRefreshing(
          upcomingEvents: currentState.upcomingEvents,
          pastEvents: currentState.pastEvents,
          birthdayEvents: currentState.birthdayEvents,
          todaysEvents: currentState.todaysEvents,
          statistics: currentState.statistics,
        ));

        final upcomingEvents = await _eventService.getUpcomingEvents();
        final pastEvents = await _eventService.getPastEvents();
        final birthdayEvents = await _eventService.getBirthdayEvents();
        final todaysEvents = await _eventService.getTodaysEvents();
        final statistics = await _eventService.getEventStatistics();

        emit(EventsLoaded(
          upcomingEvents: upcomingEvents,
          pastEvents: pastEvents,
          birthdayEvents: birthdayEvents.where((event) => event.isUpcoming).toList(),
          todaysEvents: todaysEvents,
          statistics: statistics,
        ));
      } catch (e) {
        emit(EventsError(message: e.toString()));
        // Restore previous state on error
        emit(currentState);
      }
    } else {
      await loadEvents();
    }
  }

  /// Get events for a specific date
  Future<List<EventModel>> getEventsForDate(DateTime date) async {
    try {
      return await _eventService.getEventsForDate(date);
    } catch (e) {
      return [];
    }
  }

  /// Search events
  Future<List<EventModel>> searchEvents(String query) async {
    try {
      return await _eventService.searchEvents(query);
    } catch (e) {
      return [];
    }
  }

  /// Get events by type
  Future<List<EventModel>> getEventsByType(EventType eventType) async {
    try {
      return await _eventService.getEventsByType(eventType);
    } catch (e) {
      return [];
    }
  }

  /// Get events for current month
  Future<List<EventModel>> getEventsForCurrentMonth() async {
    try {
      return await _eventService.getEventsForCurrentMonth();
    } catch (e) {
      return [];
    }
  }
}

class EventCreationCubit extends Cubit<EventCreationState> {
  final EventService _eventService;

  EventCreationCubit(this._eventService) : super(const EventCreationInitial());

  /// Create a new event
  Future<void> createEvent(CreateEventRequest request) async {
    try {
      emit(const EventCreationLoading());

      final event = await _eventService.createEvent(request);

      emit(EventCreationSuccess(event: event));
    } catch (e) {
      emit(EventCreationError(message: e.toString()));
    }
  }

  /// Reset creation state
  void resetCreationState() {
    emit(const EventCreationInitial());
  }
}

class EventDetailCubit extends Cubit<EventDetailState> {
  final EventService _eventService;

  EventDetailCubit(this._eventService) : super(const EventDetailInitial());

  /// Load event details
  Future<void> loadEventDetail(String eventId) async {
    try {
      emit(const EventDetailLoading());

      final event = await _eventService.getEventById(eventId);
      if (event != null) {
        emit(EventDetailLoaded(event: event));
      } else {
        emit(const EventDetailError(message: 'Event not found'));
      }
    } catch (e) {
      emit(EventDetailError(message: e.toString()));
    }
  }

  /// Update event
  Future<void> updateEvent(String eventId, CreateEventRequest request) async {
    final currentState = state;
    if (currentState is EventDetailLoaded) {
      try {
        emit(EventDetailUpdating(event: currentState.event));

        final updatedEvent = await _eventService.updateEvent(eventId, request);

        emit(EventDetailLoaded(event: updatedEvent));
      } catch (e) {
        emit(EventDetailError(message: e.toString()));
        // Restore previous state on error
        emit(currentState);
      }
    }
  }

  /// Delete event
  Future<void> deleteEvent(String eventId) async {
    try {
      await _eventService.deleteEvent(eventId);
      emit(const EventDetailInitial());
    } catch (e) {
      emit(EventDetailError(message: e.toString()));
    }
  }

  /// Update attendee status
  Future<void> updateAttendeeStatus(String eventId, String memberId, AttendeeStatus status) async {
    final currentState = state;
    if (currentState is EventDetailLoaded) {
      try {
        await _eventService.updateAttendeeStatus(eventId, memberId, status);
        
        // Reload event details to get updated attendees
        await loadEventDetail(eventId);
      } catch (e) {
        emit(EventDetailError(message: e.toString()));
        // Restore previous state on error
        emit(currentState);
      }
    }
  }

  /// Add attendee to event
  Future<void> addAttendee(String eventId, String memberId, AttendeeStatus status) async {
    final currentState = state;
    if (currentState is EventDetailLoaded) {
      try {
        await _eventService.addEventAttendee(eventId, memberId, status);
        
        // Reload event details to get updated attendees
        await loadEventDetail(eventId);
      } catch (e) {
        emit(EventDetailError(message: e.toString()));
        // Restore previous state on error
        emit(currentState);
      }
    }
  }

  /// Remove attendee from event
  Future<void> removeAttendee(String eventId, String memberId) async {
    final currentState = state;
    if (currentState is EventDetailLoaded) {
      try {
        await _eventService.removeEventAttendee(eventId, memberId);
        
        // Reload event details to get updated attendees
        await loadEventDetail(eventId);
      } catch (e) {
        emit(EventDetailError(message: e.toString()));
        // Restore previous state on error
        emit(currentState);
      }
    }
  }

  /// Reset detail state
  void resetDetailState() {
    emit(const EventDetailInitial());
  }
}
