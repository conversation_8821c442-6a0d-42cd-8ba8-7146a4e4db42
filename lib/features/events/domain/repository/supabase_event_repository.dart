import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/event_model.dart';
import 'event_repository.dart';

class SupabaseEventRepository implements EventRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  @override
  Future<List<EventModel>> getAllEvents() async {
    try {
      final response = await _supabase
          .from('events')
          .select('''
            *,
            organizer:family_members!organizer_id(name)
          ''')
          .order('event_date', ascending: true);

      return (response as List)
          .map((json) => EventModel.fromJson({
                ...json,
                'organizer_name': json['organizer']?['name'],
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch events: $e');
    }
  }

  @override
  Future<List<EventModel>> getUpcomingEvents() async {
    try {
      final now = DateTime.now().toIso8601String();
      final response = await _supabase
          .from('events')
          .select('''
            *,
            organizer:family_members!organizer_id(name)
          ''')
          .gte('event_date', now)
          .order('event_date', ascending: true);

      return (response as List)
          .map((json) => EventModel.fromJson({
                ...json,
                'organizer_name': json['organizer']?['name'],
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch upcoming events: $e');
    }
  }

  @override
  Future<List<EventModel>> getPastEvents() async {
    try {
      final now = DateTime.now().toIso8601String();
      final response = await _supabase
          .from('events')
          .select('''
            *,
            organizer:family_members!organizer_id(name)
          ''')
          .lt('event_date', now)
          .order('event_date', ascending: false);

      return (response as List)
          .map((json) => EventModel.fromJson({
                ...json,
                'organizer_name': json['organizer']?['name'],
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch past events: $e');
    }
  }

  @override
  Future<List<EventModel>> getEventsByType(EventType eventType) async {
    try {
      final response = await _supabase
          .from('events')
          .select('''
            *,
            organizer:family_members!organizer_id(name)
          ''')
          .eq('event_type', eventType.value)
          .order('event_date', ascending: true);

      return (response as List)
          .map((json) => EventModel.fromJson({
                ...json,
                'organizer_name': json['organizer']?['name'],
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch events by type: $e');
    }
  }

  @override
  Future<EventModel?> getEventById(String eventId) async {
    try {
      final response = await _supabase
          .from('events')
          .select('''
            *,
            organizer:family_members!organizer_id(name)
          ''')
          .eq('id', eventId)
          .maybeSingle();

      if (response == null) return null;

      return EventModel.fromJson({
        ...response,
        'organizer_name': response['organizer']?['name'],
      });
    } catch (e) {
      throw Exception('Failed to fetch event: $e');
    }
  }

  @override
  Future<EventModel> createEvent(CreateEventRequest request) async {
    try {
      final response = await _supabase
          .from('events')
          .insert(request.toJson())
          .select('''
            *,
            organizer:family_members!organizer_id(name)
          ''')
          .single();

      return EventModel.fromJson({
        ...response,
        'organizer_name': response['organizer']?['name'],
      });
    } catch (e) {
      throw Exception('Failed to create event: $e');
    }
  }

  @override
  Future<EventModel> updateEvent(String eventId, CreateEventRequest request) async {
    try {
      final response = await _supabase
          .from('events')
          .update(request.toJson())
          .eq('id', eventId)
          .select('''
            *,
            organizer:family_members!organizer_id(name)
          ''')
          .single();

      return EventModel.fromJson({
        ...response,
        'organizer_name': response['organizer']?['name'],
      });
    } catch (e) {
      throw Exception('Failed to update event: $e');
    }
  }

  @override
  Future<void> deleteEvent(String eventId) async {
    try {
      await _supabase.from('events').delete().eq('id', eventId);
    } catch (e) {
      throw Exception('Failed to delete event: $e');
    }
  }

  @override
  Future<List<EventAttendee>> getEventAttendees(String eventId) async {
    try {
      final response = await _supabase
          .from('event_attendees')
          .select('''
            *,
            member:family_members!member_id(name)
          ''')
          .eq('event_id', eventId);

      return (response as List)
          .map((json) => EventAttendee.fromJson({
                ...json,
                'member_name': json['member']?['name'] ?? '',
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch event attendees: $e');
    }
  }

  @override
  Future<void> addEventAttendee(String eventId, String memberId, AttendeeStatus status) async {
    try {
      await _supabase.from('event_attendees').insert({
        'event_id': eventId,
        'member_id': memberId,
        'status': status.value,
      });
    } catch (e) {
      throw Exception('Failed to add event attendee: $e');
    }
  }

  @override
  Future<void> updateAttendeeStatus(String eventId, String memberId, AttendeeStatus status) async {
    try {
      await _supabase
          .from('event_attendees')
          .update({'status': status.value})
          .eq('event_id', eventId)
          .eq('member_id', memberId);
    } catch (e) {
      throw Exception('Failed to update attendee status: $e');
    }
  }

  @override
  Future<void> removeEventAttendee(String eventId, String memberId) async {
    try {
      await _supabase
          .from('event_attendees')
          .delete()
          .eq('event_id', eventId)
          .eq('member_id', memberId);
    } catch (e) {
      throw Exception('Failed to remove event attendee: $e');
    }
  }

  @override
  Future<List<EventModel>> getBirthdayEvents() async {
    try {
      // Get upcoming birthdays from family members
      final now = DateTime.now();
      final currentYear = now.year;
      
      final response = await _supabase
          .from('family_members')
          .select('id, name, date_of_birth')
          .eq('is_alive', true);

      final List<EventModel> birthdayEvents = [];
      
      for (final member in response) {
        final birthDate = DateTime.parse(member['date_of_birth']);
        final thisYearBirthday = DateTime(currentYear, birthDate.month, birthDate.day);
        final nextYearBirthday = DateTime(currentYear + 1, birthDate.month, birthDate.day);
        
        // Check if birthday is upcoming this year or next year
        final upcomingBirthday = thisYearBirthday.isAfter(now) 
            ? thisYearBirthday 
            : nextYearBirthday;
            
        birthdayEvents.add(EventModel(
          id: 'birthday_${member['id']}',
          title: '${member['name']} Birthday',
          description: 'Birthday celebration for ${member['name']}',
          eventDate: upcomingBirthday,
          eventType: EventType.birthday,
          organizerId: member['id'],
          organizerName: member['name'],
        ));
      }
      
      // Sort by date
      birthdayEvents.sort((a, b) => a.eventDate.compareTo(b.eventDate));
      
      return birthdayEvents;
    } catch (e) {
      throw Exception('Failed to fetch birthday events: $e');
    }
  }

  @override
  Future<List<EventModel>> getEventsByOrganizer(String organizerId) async {
    try {
      final response = await _supabase
          .from('events')
          .select('''
            *,
            organizer:family_members!organizer_id(name)
          ''')
          .eq('organizer_id', organizerId)
          .order('event_date', ascending: true);

      return (response as List)
          .map((json) => EventModel.fromJson({
                ...json,
                'organizer_name': json['organizer']?['name'],
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch events by organizer: $e');
    }
  }
}
