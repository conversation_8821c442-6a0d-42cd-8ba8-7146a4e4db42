import '../models/event_model.dart';

abstract class EventRepository {
  /// Get all events
  Future<List<EventModel>> getAllEvents();
  
  /// Get upcoming events
  Future<List<EventModel>> getUpcomingEvents();
  
  /// Get past events
  Future<List<EventModel>> getPastEvents();
  
  /// Get events by type
  Future<List<EventModel>> getEventsByType(EventType eventType);
  
  /// Get event by ID
  Future<EventModel?> getEventById(String eventId);
  
  /// Create a new event
  Future<EventModel> createEvent(CreateEventRequest request);
  
  /// Update an existing event
  Future<EventModel> updateEvent(String eventId, CreateEventRequest request);
  
  /// Delete an event
  Future<void> deleteEvent(String eventId);
  
  /// Get event attendees
  Future<List<EventAttendee>> getEventAttendees(String eventId);
  
  /// Add attendee to event
  Future<void> addEventAttendee(String eventId, String memberId, AttendeeStatus status);
  
  /// Update attendee status
  Future<void> updateAttendeeStatus(String eventId, String memberId, AttendeeStatus status);
  
  /// Remove attendee from event
  Future<void> removeEventAttendee(String eventId, String memberId);
  
  /// Get birthday events for family members
  Future<List<EventModel>> getBirthdayEvents();
  
  /// Get events organized by a specific member
  Future<List<EventModel>> getEventsByOrganizer(String organizerId);
}
