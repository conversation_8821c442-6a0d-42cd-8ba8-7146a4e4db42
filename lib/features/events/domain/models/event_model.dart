import 'package:equatable/equatable.dart';

enum EventType {
  birthday,
  family_gathering,
  trip,
  movie,
  dining,
  festival,
  anniversary,
  wedding,
  graduation,
  other,
}

extension EventTypeExtension on EventType {
  String get displayName {
    switch (this) {
      case EventType.birthday:
        return 'Birthday';
      case EventType.family_gathering:
        return 'Family Gathering';
      case EventType.trip:
        return 'Trip';
      case EventType.movie:
        return 'Movie Night';
      case EventType.dining:
        return 'Family Dining';
      case EventType.festival:
        return 'Festival';
      case EventType.anniversary:
        return 'Anniversary';
      case EventType.wedding:
        return 'Wedding';
      case EventType.graduation:
        return 'Graduation';
      case EventType.other:
        return 'Other';
    }
  }

  String get value {
    return name;
  }

  static EventType fromString(String value) {
    return EventType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => EventType.other,
    );
  }
}

enum AttendeeStatus {
  attending,
  not_attending,
  maybe,
}

extension AttendeeStatusExtension on AttendeeStatus {
  String get displayName {
    switch (this) {
      case AttendeeStatus.attending:
        return 'Attending';
      case AttendeeStatus.not_attending:
        return 'Not Attending';
      case AttendeeStatus.maybe:
        return 'Maybe';
    }
  }

  String get value {
    return name;
  }

  static AttendeeStatus fromString(String value) {
    return AttendeeStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => AttendeeStatus.maybe,
    );
  }
}

class EventModel extends Equatable {
  final String id;
  final String title;
  final String? description;
  final DateTime eventDate;
  final EventType eventType;
  final String? location;
  final String? organizerId;
  final String? organizerName;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<EventAttendee> attendees;

  const EventModel({
    required this.id,
    required this.title,
    this.description,
    required this.eventDate,
    required this.eventType,
    this.location,
    this.organizerId,
    this.organizerName,
    this.createdAt,
    this.updatedAt,
    this.attendees = const [],
  });

  bool get isUpcoming => eventDate.isAfter(DateTime.now());
  bool get isPast => eventDate.isBefore(DateTime.now());
  bool get isToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final eventDay = DateTime(eventDate.year, eventDate.month, eventDate.day);
    return today.isAtSameMomentAs(eventDay);
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        eventDate,
        eventType,
        location,
        organizerId,
        organizerName,
        createdAt,
        updatedAt,
        attendees,
      ];

  EventModel copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? eventDate,
    EventType? eventType,
    String? location,
    String? organizerId,
    String? organizerName,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<EventAttendee>? attendees,
  }) {
    return EventModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      eventDate: eventDate ?? this.eventDate,
      eventType: eventType ?? this.eventType,
      location: location ?? this.location,
      organizerId: organizerId ?? this.organizerId,
      organizerName: organizerName ?? this.organizerName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      attendees: attendees ?? this.attendees,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'event_date': eventDate.toIso8601String(),
      'event_type': eventType.value,
      'location': location,
      'organizer_id': organizerId,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory EventModel.fromJson(Map<String, dynamic> json) {
    return EventModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      eventDate: DateTime.parse(json['event_date'] as String),
      eventType: EventTypeExtension.fromString(json['event_type'] as String),
      location: json['location'] as String?,
      organizerId: json['organizer_id'] as String?,
      organizerName: json['organizer_name'] as String?,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }
}

class EventAttendee extends Equatable {
  final String eventId;
  final String memberId;
  final String memberName;
  final AttendeeStatus status;
  final DateTime? createdAt;

  const EventAttendee({
    required this.eventId,
    required this.memberId,
    required this.memberName,
    required this.status,
    this.createdAt,
  });

  @override
  List<Object?> get props => [
        eventId,
        memberId,
        memberName,
        status,
        createdAt,
      ];

  EventAttendee copyWith({
    String? eventId,
    String? memberId,
    String? memberName,
    AttendeeStatus? status,
    DateTime? createdAt,
  }) {
    return EventAttendee(
      eventId: eventId ?? this.eventId,
      memberId: memberId ?? this.memberId,
      memberName: memberName ?? this.memberName,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'event_id': eventId,
      'member_id': memberId,
      'status': status.value,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  factory EventAttendee.fromJson(Map<String, dynamic> json) {
    return EventAttendee(
      eventId: json['event_id'] as String,
      memberId: json['member_id'] as String,
      memberName: json['member_name'] as String? ?? '',
      status: AttendeeStatusExtension.fromString(json['status'] as String),
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
    );
  }
}

class CreateEventRequest extends Equatable {
  final String title;
  final String? description;
  final DateTime eventDate;
  final EventType eventType;
  final String? location;
  final String? organizerId;

  const CreateEventRequest({
    required this.title,
    this.description,
    required this.eventDate,
    required this.eventType,
    this.location,
    this.organizerId,
  });

  @override
  List<Object?> get props => [
        title,
        description,
        eventDate,
        eventType,
        location,
        organizerId,
      ];

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'event_date': eventDate.toIso8601String(),
      'event_type': eventType.value,
      'location': location,
      'organizer_id': organizerId,
    };
  }
}
