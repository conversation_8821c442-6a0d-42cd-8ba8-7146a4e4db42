import '../models/event_model.dart';
import '../repository/event_repository.dart';

class EventService {
  final EventRepository _repository;

  EventService(this._repository);

  /// Get all events with attendees
  Future<List<EventModel>> getAllEvents() async {
    final events = await _repository.getAllEvents();
    
    // Load attendees for each event
    for (int i = 0; i < events.length; i++) {
      final attendees = await _repository.getEventAttendees(events[i].id);
      events[i] = events[i].copyWith(attendees: attendees);
    }
    
    return events;
  }

  /// Get upcoming events with attendees
  Future<List<EventModel>> getUpcomingEvents() async {
    final events = await _repository.getUpcomingEvents();
    
    // Load attendees for each event
    for (int i = 0; i < events.length; i++) {
      final attendees = await _repository.getEventAttendees(events[i].id);
      events[i] = events[i].copyWith(attendees: attendees);
    }
    
    return events;
  }

  /// Get past events with attendees
  Future<List<EventModel>> getPastEvents() async {
    final events = await _repository.getPastEvents();
    
    // Load attendees for each event
    for (int i = 0; i < events.length; i++) {
      final attendees = await _repository.getEventAttendees(events[i].id);
      events[i] = events[i].copyWith(attendees: attendees);
    }
    
    return events;
  }

  /// Get events by type
  Future<List<EventModel>> getEventsByType(EventType eventType) async {
    return await _repository.getEventsByType(eventType);
  }

  /// Get event by ID with attendees
  Future<EventModel?> getEventById(String eventId) async {
    final event = await _repository.getEventById(eventId);
    if (event == null) return null;
    
    final attendees = await _repository.getEventAttendees(eventId);
    return event.copyWith(attendees: attendees);
  }

  /// Create a new event
  Future<EventModel> createEvent(CreateEventRequest request) async {
    return await _repository.createEvent(request);
  }

  /// Update an existing event
  Future<EventModel> updateEvent(String eventId, CreateEventRequest request) async {
    return await _repository.updateEvent(eventId, request);
  }

  /// Delete an event
  Future<void> deleteEvent(String eventId) async {
    await _repository.deleteEvent(eventId);
  }

  /// Get event attendees
  Future<List<EventAttendee>> getEventAttendees(String eventId) async {
    return await _repository.getEventAttendees(eventId);
  }

  /// Add attendee to event
  Future<void> addEventAttendee(String eventId, String memberId, AttendeeStatus status) async {
    await _repository.addEventAttendee(eventId, memberId, status);
  }

  /// Update attendee status
  Future<void> updateAttendeeStatus(String eventId, String memberId, AttendeeStatus status) async {
    await _repository.updateAttendeeStatus(eventId, memberId, status);
  }

  /// Remove attendee from event
  Future<void> removeEventAttendee(String eventId, String memberId) async {
    await _repository.removeEventAttendee(eventId, memberId);
  }

  /// Get birthday events for family members
  Future<List<EventModel>> getBirthdayEvents() async {
    return await _repository.getBirthdayEvents();
  }

  /// Get events organized by a specific member
  Future<List<EventModel>> getEventsByOrganizer(String organizerId) async {
    return await _repository.getEventsByOrganizer(organizerId);
  }

  /// Get events grouped by category
  Future<Map<String, List<EventModel>>> getEventsGrouped() async {
    final allEvents = await getAllEvents();
    final upcomingEvents = allEvents.where((event) => event.isUpcoming).toList();
    final pastEvents = allEvents.where((event) => event.isPast).toList();
    final birthdayEvents = await getBirthdayEvents();
    
    return {
      'upcoming': upcomingEvents,
      'past': pastEvents,
      'birthdays': birthdayEvents.where((event) => event.isUpcoming).toList(),
    };
  }

  /// Get today's events
  Future<List<EventModel>> getTodaysEvents() async {
    final allEvents = await getAllEvents();
    return allEvents.where((event) => event.isToday).toList();
  }

  /// Get events for a specific date
  Future<List<EventModel>> getEventsForDate(DateTime date) async {
    final allEvents = await getAllEvents();
    return allEvents.where((event) {
      final eventDay = DateTime(event.eventDate.year, event.eventDate.month, event.eventDate.day);
      final targetDay = DateTime(date.year, date.month, date.day);
      return eventDay.isAtSameMomentAs(targetDay);
    }).toList();
  }

  /// Get events for current month
  Future<List<EventModel>> getEventsForCurrentMonth() async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    final allEvents = await getAllEvents();
    return allEvents.where((event) {
      return event.eventDate.isAfter(startOfMonth.subtract(const Duration(days: 1))) &&
             event.eventDate.isBefore(endOfMonth.add(const Duration(days: 1)));
    }).toList();
  }

  /// Search events by title or description
  Future<List<EventModel>> searchEvents(String query) async {
    final allEvents = await getAllEvents();
    final lowercaseQuery = query.toLowerCase();
    
    return allEvents.where((event) {
      return event.title.toLowerCase().contains(lowercaseQuery) ||
             (event.description?.toLowerCase().contains(lowercaseQuery) ?? false) ||
             (event.location?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  /// Get event statistics
  Future<Map<String, int>> getEventStatistics() async {
    final allEvents = await getAllEvents();
    final upcomingCount = allEvents.where((event) => event.isUpcoming).length;
    final pastCount = allEvents.where((event) => event.isPast).length;
    final todayCount = allEvents.where((event) => event.isToday).length;
    
    final eventTypeCount = <String, int>{};
    for (final event in allEvents) {
      final typeName = event.eventType.displayName;
      eventTypeCount[typeName] = (eventTypeCount[typeName] ?? 0) + 1;
    }
    
    return {
      'total': allEvents.length,
      'upcoming': upcomingCount,
      'past': pastCount,
      'today': todayCount,
      ...eventTypeCount,
    };
  }
}
