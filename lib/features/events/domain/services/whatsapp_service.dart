import 'package:url_launcher/url_launcher.dart';
import '../models/event_model.dart';
import '../../../family/domain/models/family_member_model.dart';

class WhatsAppService {
  /// Generate birthday message for a family member
  String generateBirthdayMessage(String memberName, DateTime birthdayDate) {
    final age = DateTime.now().year - birthdayDate.year;
    final isToday = _isToday(birthdayDate);
    
    if (isToday) {
      return "🎉 Happy Birthday $memberName! 🎂\n\nWishing you a wonderful day filled with happiness and joy! May this new year of your life bring you lots of love, success, and beautiful memories. 🎈✨\n\nHave an amazing celebration! 🥳";
    } else {
      final daysUntil = _getDaysUntil(birthdayDate);
      return "🎂 Hey! Just wanted to remind you that $member<PERSON><PERSON>'s birthday is coming up in $daysUntil days! 📅\n\nLet's make sure to wish them a happy birthday! 🎉";
    }
  }

  /// Generate event message based on event type
  String generateEventMessage(EventModel event, String? memberName) {
    final eventDate = event.eventDate;
    final isToday = _isToday(eventDate);
    final isTomorrow = _isTomorrow(eventDate);
    
    String timePrefix;
    if (isToday) {
      timePrefix = "Today";
    } else if (isTomorrow) {
      timePrefix = "Tomorrow";
    } else {
      final daysUntil = _getDaysUntil(eventDate);
      timePrefix = "In $daysUntil days";
    }

    switch (event.eventType) {
      case EventType.birthday:
        return generateBirthdayMessage(event.title.replaceAll(' Birthday', ''), eventDate);
      
      case EventType.family_gathering:
        return "👨‍👩‍👧‍👦 $timePrefix we have a family gathering! 🏠\n\n📍 Event: ${event.title}\n📅 Date: ${_formatDate(eventDate)}\n📍 Location: ${event.location ?? 'TBD'}\n\nLooking forward to seeing everyone! Let me know if you can make it! 😊";
      
      case EventType.dining:
        return "🍽️ $timePrefix we have a family dinner planned! 🥘\n\n📍 Event: ${event.title}\n📅 Date: ${_formatDate(eventDate)}\n📍 Location: ${event.location ?? 'Home'}\n\nCome hungry! Can't wait to enjoy good food and great company! 😋";
      
      case EventType.movie:
        return "🎬 $timePrefix it's movie night! 🍿\n\n📍 Event: ${event.title}\n📅 Date: ${_formatDate(eventDate)}\n📍 Location: ${event.location ?? 'Home'}\n\nBring your favorite snacks! It's going to be a fun evening! 🎭";
      
      case EventType.trip:
        return "✈️ $timePrefix we have an exciting trip planned! 🧳\n\n📍 Event: ${event.title}\n📅 Date: ${_formatDate(eventDate)}\n📍 Destination: ${event.location ?? 'TBD'}\n\nPack your bags and get ready for an adventure! 🌟";
      
      case EventType.festival:
        return "🎊 $timePrefix we're celebrating a festival! 🪔\n\n📍 Event: ${event.title}\n📅 Date: ${_formatDate(eventDate)}\n📍 Location: ${event.location ?? 'TBD'}\n\nLet's celebrate our traditions together! 🙏";
      
      case EventType.anniversary:
        return "💕 $timePrefix is a special anniversary! 💐\n\n📍 Event: ${event.title}\n📅 Date: ${_formatDate(eventDate)}\n📍 Location: ${event.location ?? 'TBD'}\n\nLet's celebrate this beautiful milestone together! 🥂";
      
      case EventType.wedding:
        return "💒 $timePrefix we have a wedding celebration! 👰‍♀️🤵‍♂️\n\n📍 Event: ${event.title}\n📅 Date: ${_formatDate(eventDate)}\n📍 Location: ${event.location ?? 'TBD'}\n\nLet's celebrate love and new beginnings! 💍✨";
      
      case EventType.graduation:
        return "🎓 $timePrefix we're celebrating a graduation! 📚\n\n📍 Event: ${event.title}\n📅 Date: ${_formatDate(eventDate)}\n📍 Location: ${event.location ?? 'TBD'}\n\nSo proud of this achievement! Let's celebrate! 🌟";
      
      case EventType.other:
      default:
        return "📅 $timePrefix we have an event! 🎉\n\n📍 Event: ${event.title}\n📅 Date: ${_formatDate(eventDate)}\n📍 Location: ${event.location ?? 'TBD'}\n\nHope to see you there! 😊";
    }
  }

  /// Launch WhatsApp with pre-filled message
  Future<bool> sendWhatsAppMessage({
    String? phoneNumber,
    required String message,
  }) async {
    try {
      final encodedMessage = Uri.encodeComponent(message);

      // Use the most reliable WhatsApp URL format
      String whatsappUrl;
      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        final formattedNumber = _formatPhoneNumber(phoneNumber);
        whatsappUrl = 'https://wa.me/$formattedNumber?text=$encodedMessage';
      } else {
        whatsappUrl = 'https://wa.me/?text=$encodedMessage';
      }

      print('WhatsApp URL: $whatsappUrl'); // Debug output

      final uri = Uri.parse(whatsappUrl);

      // Use the most compatible launch mode
      final launched = await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );

      print('WhatsApp launch result: $launched'); // Debug output
      return launched;

    } catch (e) {
      print('WhatsApp launch error: $e'); // Debug output

      // Fallback: try to open WhatsApp app directly
      try {
        final whatsappAppUri = Uri.parse('whatsapp://');
        final canLaunchApp = await canLaunchUrl(whatsappAppUri);
        print('Can launch WhatsApp app: $canLaunchApp'); // Debug output

        if (canLaunchApp) {
          final launched = await launchUrl(whatsappAppUri);
          print('WhatsApp app launch result: $launched'); // Debug output
          return launched;
        }
      } catch (e2) {
        print('WhatsApp app launch error: $e2'); // Debug output
      }

      throw Exception('Failed to send WhatsApp message: $e');
    }
  }

  /// Send birthday message via WhatsApp
  Future<bool> sendBirthdayMessage({
    required String memberName,
    required DateTime birthdayDate,
    String? phoneNumber,
  }) async {
    final message = generateBirthdayMessage(memberName, birthdayDate);
    return await sendWhatsAppMessage(
      phoneNumber: phoneNumber,
      message: message,
    );
  }

  /// Send event message via WhatsApp
  Future<bool> sendEventMessage({
    required EventModel event,
    String? phoneNumber,
    String? memberName,
  }) async {
    final message = generateEventMessage(event, memberName);
    return await sendWhatsAppMessage(
      phoneNumber: phoneNumber,
      message: message,
    );
  }

  // Helper methods
  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  bool _isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return date.year == tomorrow.year && date.month == tomorrow.month && date.day == tomorrow.day;
  }

  int _getDaysUntil(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    return targetDate.difference(today).inDays;
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year} at ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  String _formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Add country code if not present (assuming India +91)
    if (cleaned.length == 10) {
      cleaned = '91$cleaned';
    } else if (cleaned.startsWith('0')) {
      cleaned = '91${cleaned.substring(1)}';
    } else if (cleaned.startsWith('91') && cleaned.length == 12) {
      // Already has country code
    } else if (cleaned.startsWith('+91')) {
      cleaned = cleaned.substring(1);
    }
    
    return cleaned;
  }
}
