import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../widgets/events_widget.dart';
import '../widgets/events_shimmer.dart';
import '../logic/events/events_cubit.dart';
import '../logic/events/events_state.dart';
import '../domain/services/event_service.dart';
import '../domain/repository/supabase_event_repository.dart';
import 'add_event_screen.dart';

class EventsScreen extends StatelessWidget {
  const EventsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EventsCubit(
        EventService(SupabaseEventRepository()),
      )..loadEvents(),
      child: const EventsView(),
    );
  }
}

class EventsView extends StatelessWidget {
  const EventsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7FAFC),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.event,
                          color: Color(0xFF0D141C),
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Events',
                        style: GoogleFonts.plusJakartaSans(
                          fontWeight: FontWeight.w700,
                          fontSize: 18,
                          color: const Color(0xFF0D141C),
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    onPressed: () {
                      _navigateToAddEvent(context);
                    },
                    icon: const Icon(
                      Icons.add,
                      color: Color(0xFF0D141C),
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),

            // Events Content
            Expanded(
              child: BlocBuilder<EventsCubit, EventsState>(
                builder: (context, state) {
                  if (state is EventsLoading) {
                    return const EventsShimmer();
                  } else if (state is EventsError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.red.shade300,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading events',
                            style: GoogleFonts.plusJakartaSans(
                              fontWeight: FontWeight.w600,
                              fontSize: 18,
                              color: const Color(0xFF0D141C),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.message,
                            style: GoogleFonts.plusJakartaSans(
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              color: const Color(0xFF4A739C),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              context.read<EventsCubit>().loadEvents();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0D80F2),
                              foregroundColor: Colors.white,
                            ),
                            child: Text(
                              'Retry',
                              style: GoogleFonts.plusJakartaSans(
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  } else if (state is EventsLoaded) {
                    return EventsWidget(
                      upcomingEvents: state.upcomingEvents,
                      pastEvents: state.pastEvents,
                      birthdayEvents: state.birthdayEvents,
                      isRefreshing: false,
                      onRefresh: () => context.read<EventsCubit>().refreshEvents(),
                    );
                  } else if (state is EventsRefreshing) {
                    return EventsWidget(
                      upcomingEvents: state.upcomingEvents,
                      pastEvents: state.pastEvents,
                      birthdayEvents: state.birthdayEvents,
                      isRefreshing: true,
                      onRefresh: () => context.read<EventsCubit>().refreshEvents(),
                    );
                  }

                  return const EventsWidget(
                    upcomingEvents: [],
                    pastEvents: [],
                    birthdayEvents: [],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToAddEvent(BuildContext context) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddEventScreen(),
      ),
    );

    // Refresh events when returning from add event screen
    if (context.mounted) {
      context.read<EventsCubit>().refreshEvents();
    }
  }
}
