import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../domain/models/event_model.dart';
import '../domain/services/whatsapp_service.dart';
import '../screens/add_event_screen.dart';

class EventsWidget extends StatefulWidget {
  final List<EventModel> upcomingEvents;
  final List<EventModel> pastEvents;
  final List<EventModel> birthdayEvents;
  final bool isRefreshing;
  final VoidCallback? onRefresh;

  const EventsWidget({
    super.key,
    this.upcomingEvents = const [],
    this.pastEvents = const [],
    this.birthdayEvents = const [],
    this.isRefreshing = false,
    this.onRefresh,
  });

  @override
  State<EventsWidget> createState() => _EventsWidgetState();
}

class _EventsWidgetState extends State<EventsWidget> {
  bool isMonthlyView = true;
  DateTime _currentMonth = DateTime.now();
  DateTime? _selectedDate;
  final WhatsAppService _whatsappService = WhatsAppService();

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        widget.onRefresh?.call();
      },
      color: const Color(0xFF0D80F2),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            // View Toggle
            _buildViewToggle(),

            // Calendar
            _buildCalendar(),

            // Birthday Notifications
            if (widget.birthdayEvents.isNotEmpty) _buildBirthdayEvents(),

            // Upcoming Events
            _buildUpcomingEvents(),

            // Past Events
            _buildPastEvents(),

            // Bottom spacing for navigation
            const SizedBox(height: 100),
          ],
        ),
      ),
    );
  }

  Widget _buildViewToggle() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: const Color(0xFFE8EDF5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => isMonthlyView = true),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isMonthlyView ? Colors.white : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: isMonthlyView ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 0),
                    ),
                  ] : null,
                ),
                child: Text(
                  'Monthly',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: const Color(0xFF0D141C),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => isMonthlyView = false),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: !isMonthlyView ? Colors.white : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: !isMonthlyView ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 0),
                    ),
                  ] : null,
                ),
                child: Text(
                  'Weekly',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: const Color(0xFF4A739C),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Calendar Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: _previousMonth,
                icon: const Icon(
                  Icons.arrow_back_ios,
                  size: 18,
                  color: Color(0xFF0D141C),
                ),
              ),
              Text(
                DateFormat('MMMM yyyy').format(_currentMonth),
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w700,
                  fontSize: 16,
                  color: const Color(0xFF0D141C),
                ),
              ),
              IconButton(
                onPressed: _nextMonth,
                icon: const Icon(
                  Icons.arrow_forward_ios,
                  size: 18,
                  color: Color(0xFF0D141C),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Calendar Grid
          _buildCalendarGrid(),
        ],
      ),
    );
  }

  Widget _buildCalendarGrid() {
    final weekDays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
    final daysInMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 0).day;
    final firstDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month, 1);
    final firstWeekday = firstDayOfMonth.weekday % 7; // 0 = Sunday, 1 = Monday, etc.
    final today = DateTime.now();

    // Calculate how many weeks we need to show
    final weeksNeeded = ((firstWeekday + daysInMonth - 1) / 7).ceil();
    final totalCells = weeksNeeded * 7;
    final List<DateTime?> calendarDays = List.filled(totalCells, null);

    // Fill in the days of the current month
    for (int i = 0; i < daysInMonth; i++) {
      final dayIndex = firstWeekday + i;
      if (dayIndex < totalCells) {
        calendarDays[dayIndex] = DateTime(_currentMonth.year, _currentMonth.month, i + 1);
      }
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Week days header
        SizedBox(
          height: 32,
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              childAspectRatio: 1.0,
            ),
            itemCount: 7,
            itemBuilder: (context, index) {
              return Center(
                child: Text(
                  weekDays[index],
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w700,
                    fontSize: 12,
                    color: const Color(0xFF4A739C),
                  ),
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 8),

        // Calendar days grid
        SizedBox(
          height: weeksNeeded * 40.0 + (weeksNeeded - 1) * 4.0, // Height for weeks + spacing
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              childAspectRatio: 1.0,
              mainAxisSpacing: 4,
              crossAxisSpacing: 2,
            ),
            itemCount: totalCells,
            itemBuilder: (context, index) {
              final date = calendarDays[index];

              if (date == null) {
                return Container();
              }

              final isToday = date.year == today.year &&
                             date.month == today.month &&
                             date.day == today.day;
              final isSelected = _selectedDate != null &&
                               date.year == _selectedDate!.year &&
                               date.month == _selectedDate!.month &&
                               date.day == _selectedDate!.day;
              final hasEvent = _hasEventOnDate(date);
              final eventsCount = _getEventsCountOnDate(date);

              return GestureDetector(
                onTap: () => _onDateTapped(date),
                child: Container(
                  decoration: BoxDecoration(
                    color: isToday
                        ? const Color(0xFF0D80F2)
                        : isSelected
                            ? const Color(0xFF0D80F2).withValues(alpha: 0.15)
                            : hasEvent
                                ? const Color(0xFF0D80F2).withValues(alpha: 0.05)
                                : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    border: isSelected && !isToday
                        ? Border.all(color: const Color(0xFF0D80F2), width: 2)
                        : hasEvent && !isToday && !isSelected
                            ? Border.all(color: const Color(0xFF0D80F2).withValues(alpha: 0.3), width: 1)
                            : null,
                  ),
                  child: Stack(
                    children: [
                      // Date number
                      Center(
                        child: Text(
                          date.day.toString(),
                          style: GoogleFonts.plusJakartaSans(
                            fontWeight: isToday || hasEvent ? FontWeight.w600 : FontWeight.w500,
                            fontSize: 14,
                            color: isToday
                                ? Colors.white
                                : hasEvent
                                    ? const Color(0xFF0D80F2)
                                    : const Color(0xFF0D141C),
                          ),
                        ),
                      ),

                      // Event indicators
                      if (hasEvent && eventsCount > 0)
                        Positioned(
                          bottom: 4,
                          left: 0,
                          right: 0,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (eventsCount == 1)
                                Container(
                                  width: 6,
                                  height: 6,
                                  decoration: BoxDecoration(
                                    color: isToday
                                        ? Colors.white
                                        : const Color(0xFF0D80F2),
                                    shape: BoxShape.circle,
                                  ),
                                )
                              else if (eventsCount == 2)
                                Flexible(
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                        width: 4,
                                        height: 4,
                                        decoration: BoxDecoration(
                                          color: isToday
                                              ? Colors.white
                                              : const Color(0xFF0D80F2),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: 2),
                                      Container(
                                        width: 4,
                                        height: 4,
                                        decoration: BoxDecoration(
                                          color: isToday
                                              ? Colors.white
                                              : const Color(0xFF0D80F2),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              else
                                Flexible(
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                        width: 3,
                                        height: 3,
                                        decoration: BoxDecoration(
                                          color: isToday
                                              ? Colors.white
                                              : const Color(0xFF0D80F2),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: 1),
                                      Container(
                                        width: 3,
                                        height: 3,
                                        decoration: BoxDecoration(
                                          color: isToday
                                              ? Colors.white
                                              : const Color(0xFF0D80F2),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: 1),
                                      Container(
                                        width: 3,
                                        height: 3,
                                        decoration: BoxDecoration(
                                          color: isToday
                                              ? Colors.white
                                              : const Color(0xFF0D80F2),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildUpcomingEvents() {
    if (widget.upcomingEvents.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
            child: Text(
              'Upcoming Events',
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w700,
                fontSize: 18,
                color: const Color(0xFF0D141C),
              ),
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(
                  Icons.event_available,
                  size: 48,
                  color: Colors.grey.shade300,
                ),
                const SizedBox(height: 16),
                Text(
                  'No Upcoming Events',
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: const Color(0xFF0D141C),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Create your first family event!',
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    color: const Color(0xFF4A739C),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Text(
            'Upcoming Events',
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color: const Color(0xFF0D141C),
            ),
          ),
        ),
        ...widget.upcomingEvents.map((event) => _buildEventCard(event)),
      ],
    );
  }

  Widget _buildBirthdayEvents() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Row(
            children: [
              Icon(
                Icons.cake,
                size: 20,
                color: const Color(0xFF0D80F2),
              ),
              const SizedBox(width: 8),
              Text(
                'Upcoming Birthdays',
                style: GoogleFonts.plusJakartaSans(
                  fontWeight: FontWeight.w700,
                  fontSize: 18,
                  color: const Color(0xFF0D141C),
                ),
              ),
            ],
          ),
        ),
        ...widget.birthdayEvents.map((event) => _buildBirthdayCard(event)),
      ],
    );
  }

  Widget _buildBirthdayCard(EventModel event) {
    final daysUntil = event.eventDate.difference(DateTime.now()).inDays;
    final isToday = daysUntil == 0;
    final isTomorrow = daysUntil == 1;

    String timeText;
    if (isToday) {
      timeText = 'Today';
    } else if (isTomorrow) {
      timeText = 'Tomorrow';
    } else {
      timeText = 'in $daysUntil days';
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isToday ? const Color(0xFF0D80F2) : Colors.transparent,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: isToday ? const Color(0xFF0D80F2) : const Color(0xFFFFE4E1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.cake,
              size: 24,
              color: isToday ? Colors.white : const Color(0xFFFF6B6B),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.title,
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                    color: const Color(0xFF0D141C),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  DateFormat('MMM dd, yyyy').format(event.eventDate),
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    color: const Color(0xFF4A739C),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isToday ? const Color(0xFF0D80F2) : const Color(0xFFE8EDF5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              timeText,
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w500,
                fontSize: 12,
                color: isToday ? Colors.white : const Color(0xFF4A739C),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // WhatsApp icon button
          GestureDetector(
            onTap: () => _sendBirthdayWhatsApp(event),
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: const Color(0xFF25D366),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.message,
                size: 18,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPastEvents() {
    if (widget.pastEvents.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
            child: Text(
              'Past Events',
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w700,
                fontSize: 18,
                color: const Color(0xFF0D141C),
              ),
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(
                  Icons.history,
                  size: 48,
                  color: Colors.grey.shade300,
                ),
                const SizedBox(height: 16),
                Text(
                  'No Past Events',
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: const Color(0xFF0D141C),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Your event history will appear here',
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    color: const Color(0xFF4A739C),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Text(
            'Past Events',
            style: GoogleFonts.plusJakartaSans(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color: const Color(0xFF0D141C),
            ),
          ),
        ),
        ...widget.pastEvents.map((event) => _buildEventCard(event)),
      ],
    );
  }

  Widget _buildEventCard(dynamic event) {
    String title;
    String date;
    IconData icon;
    String? location;
    String? organizer;

    if (event is EventModel) {
      title = event.title;
      date = DateFormat('MMM dd, yyyy • HH:mm').format(event.eventDate);
      icon = _getEventIcon(event.eventType);
      location = event.location;
      organizer = event.organizerName;
    } else if (event is Map<String, dynamic>) {
      title = event['title'] as String;
      date = event['date'] as String;
      icon = event['icon'] as IconData;
      location = null;
      organizer = null;
    } else {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: const Color(0xFFE8EDF5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 24,
              color: const Color(0xFF0D141C),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                    color: const Color(0xFF0D141C),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  date,
                  style: GoogleFonts.plusJakartaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    color: const Color(0xFF4A739C),
                  ),
                ),
                if (location != null) ...[
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 12,
                        color: const Color(0xFF4A739C),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          location,
                          style: GoogleFonts.plusJakartaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                            color: const Color(0xFF4A739C),
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
                if (organizer != null) ...[
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 12,
                        color: const Color(0xFF4A739C),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          'Organized by $organizer',
                          style: GoogleFonts.plusJakartaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                            color: const Color(0xFF4A739C),
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
          // Add WhatsApp icon button only for EventModel (not for Map)
          if (event is EventModel) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: () => _sendEventWhatsApp(event),
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: const Color(0xFF25D366),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.message,
                  size: 18,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getEventIcon(EventType eventType) {
    switch (eventType) {
      case EventType.birthday:
        return Icons.cake;
      case EventType.family_gathering:
        return Icons.family_restroom;
      case EventType.trip:
        return Icons.flight;
      case EventType.movie:
        return Icons.movie;
      case EventType.dining:
        return Icons.restaurant;
      case EventType.festival:
        return Icons.celebration;
      case EventType.anniversary:
        return Icons.favorite;
      case EventType.wedding:
        return Icons.favorite_border;
      case EventType.graduation:
        return Icons.school;
      case EventType.other:
        return Icons.event;
    }
  }

  // Calendar navigation methods
  void _previousMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
    });
  }

  void _nextMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
    });
  }

  // Check if a date has events
  bool _hasEventOnDate(DateTime date) {
    final allEvents = [...widget.upcomingEvents, ...widget.pastEvents, ...widget.birthdayEvents];
    return allEvents.any((event) {
      final eventDate = event.eventDate;
      return eventDate.year == date.year &&
             eventDate.month == date.month &&
             eventDate.day == date.day;
    });
  }

  // Get count of events on a specific date
  int _getEventsCountOnDate(DateTime date) {
    final allEvents = [...widget.upcomingEvents, ...widget.pastEvents, ...widget.birthdayEvents];
    return allEvents.where((event) {
      final eventDate = event.eventDate;
      return eventDate.year == date.year &&
             eventDate.month == date.month &&
             eventDate.day == date.day;
    }).length;
  }

  // Handle date tap
  void _onDateTapped(DateTime date) {
    setState(() {
      _selectedDate = date;
    });

    // Show bottom sheet with options
    _showDateOptionsBottomSheet(date);
  }

  void _showDateOptionsBottomSheet(DateTime date) {
    final eventsOnDate = _getEventsOnDate(date);
    final formattedDate = DateFormat('EEEE, MMMM dd, yyyy').format(date);
    final isToday = DateTime.now().day == date.day &&
                   DateTime.now().month == date.month &&
                   DateTime.now().year == date.year;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(20, 12, 20, 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Date header with icon
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFF0D80F2).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            isToday ? Icons.today : Icons.calendar_today,
                            color: const Color(0xFF0D80F2),
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                formattedDate,
                                style: GoogleFonts.plusJakartaSans(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 18,
                                  color: const Color(0xFF0D141C),
                                ),
                              ),
                              if (isToday)
                                Text(
                                  'Today',
                                  style: GoogleFonts.plusJakartaSans(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                    color: const Color(0xFF0D80F2),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Add event button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).pop();
                          _navigateToAddEvent(date);
                        },
                        icon: const Icon(Icons.add, size: 20),
                        label: Text(
                          'Add Event',
                          style: GoogleFonts.plusJakartaSans(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0D80F2),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                      ),
                    ),

                  if (eventsOnDate.isNotEmpty) ...[
                    const SizedBox(height: 20),
                    Text(
                      'Events on this day',
                      style: GoogleFonts.plusJakartaSans(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: const Color(0xFF0D141C),
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...eventsOnDate.map((event) => Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF7FAFC),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: const Color(0xFFE8EDF5)),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: const Color(0xFFE8EDF5),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Icon(
                              _getEventIcon(event.eventType),
                              size: 20,
                              color: const Color(0xFF0D141C),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  event.title,
                                  style: GoogleFonts.plusJakartaSans(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                    color: const Color(0xFF0D141C),
                                  ),
                                ),
                                if (event.location != null) ...[
                                  const SizedBox(height: 2),
                                  Text(
                                    event.location!,
                                    style: GoogleFonts.plusJakartaSans(
                                      fontWeight: FontWeight.w400,
                                      fontSize: 12,
                                      color: const Color(0xFF4A739C),
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ],
                            ),
                          ),
                          Text(
                            DateFormat('HH:mm').format(event.eventDate),
                            style: GoogleFonts.plusJakartaSans(
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                              color: const Color(0xFF4A739C),
                            ),
                          ),
                          const SizedBox(width: 8),
                          // WhatsApp icon button
                          GestureDetector(
                            onTap: () {
                              Navigator.pop(context); // Close bottom sheet first
                              if (event.eventType == EventType.birthday) {
                                _sendBirthdayWhatsApp(event);
                              } else {
                                _sendEventWhatsApp(event);
                              }
                            },
                            child: Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                color: const Color(0xFF25D366),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: const Icon(
                                Icons.message,
                                size: 16,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )),
                  ],

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<EventModel> _getEventsOnDate(DateTime date) {
    final allEvents = [...widget.upcomingEvents, ...widget.pastEvents, ...widget.birthdayEvents];
    return allEvents.where((event) {
      final eventDate = event.eventDate;
      return eventDate.year == date.year &&
             eventDate.month == date.month &&
             eventDate.day == date.day;
    }).toList();
  }

  // WhatsApp integration methods
  Future<void> _sendBirthdayWhatsApp(EventModel event) async {
    try {
      // Extract the person's name from the birthday event title
      final memberName = event.title.replaceAll(' Birthday', '').replaceAll('Birthday', '').trim();

      await _whatsappService.sendBirthdayMessage(
        memberName: memberName,
        birthdayDate: event.eventDate,
        // phoneNumber: null, // Will open WhatsApp without specific contact
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'WhatsApp opened with birthday message for $memberName!',
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            backgroundColor: const Color(0xFF25D366),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to open WhatsApp: $e',
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _sendEventWhatsApp(EventModel event) async {
    try {
      await _whatsappService.sendEventMessage(
        event: event,
        // phoneNumber: null, // Will open WhatsApp without specific contact
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'WhatsApp opened with event message for ${event.title}!',
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            backgroundColor: const Color(0xFF25D366),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to open WhatsApp: $e',
              style: GoogleFonts.plusJakartaSans(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _navigateToAddEvent(DateTime selectedDate) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEventScreen(preselectedDate: selectedDate),
      ),
    ).then((_) {
      // Refresh events when returning
      widget.onRefresh?.call();
    });
  }
}
