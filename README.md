# Kerala Family App 🏡

A beautiful Flutter application designed to connect Kerala families, preserve heritage, and celebrate traditions. Built with modern design principles following clean architecture patterns and based on Figma designs.

## 🏗️ Architecture

This project follows a **feature-based folder architecture** where each feature has its own dedicated folder containing:

- **UI components** (screens, widgets)
- **Business logic** (controllers, services, BLoC)
- **Data models** and entities
- **Repository** and data access layers

### Project Structure

```
lib/
├── core/                           # Core functionality shared across features
│   ├── constants/                  # App-wide constants
│   ├── di/                        # Dependency injection setup
│   ├── error/                     # Error handling (failures, exceptions)
│   ├── network/                   # Network client and configuration
│   ├── router/                    # App routing configuration
│   ├── theme/                     # App theming (colors, text styles, themes)
│   ├── usecases/                  # Base use case classes
│   ├── utils/                     # Utility functions
│   └── widgets/                   # Shared UI components
├── features/                      # Feature modules
│   ├── auth/                      # Authentication
│   ├── budget/                    # Budget tracking
│   ├── calendar/                  # Family calendar and events
│   ├── chat/                      # Family communication
│   ├── emergency/                 # Emergency contacts
│   ├── family_members/            # Family member management
│   ├── gallery/                   # Photo gallery
│   ├── home/                      # Home dashboard
│   ├── profile/                   # User profile
│   ├── settings/                  # App settings
│   ├── shopping/                  # Shopping lists
│   └── tasks/                     # Task management
└── main.dart                      # App entry point
```

### Feature Structure

Each feature follows Clean Architecture principles:

```
feature_name/
├── data/
│   ├── datasources/              # Remote and local data sources
│   ├── models/                   # Data models with JSON serialization
│   └── repositories/             # Repository implementations
├── domain/
│   ├── entities/                 # Business entities
│   ├── repositories/             # Repository interfaces
│   └── usecases/                 # Business use cases
└── presentation/
    ├── bloc/                     # State management (BLoC/Cubit)
    ├── pages/                    # Screen widgets
    └── widgets/                  # Feature-specific widgets
```

## 🚀 Features

### Core Features
- **Family Members** - Manage family member profiles with roles and information
- **Shared Calendar** - Family events, appointments, and important dates
- **Task Management** - Household chores and responsibilities with assignments
- **Shopping Lists** - Shared grocery and shopping lists with real-time updates
- **Family Chat** - Communication between family members
- **Photo Gallery** - Shared family photos and memories
- **Budget Tracker** - Family expense management and financial planning
- **Emergency Contacts** - Important contact information for emergencies

### Technical Features
- **Clean Architecture** with separation of concerns
- **Feature-based** folder structure for scalability
- **State Management** using BLoC pattern
- **Dependency Injection** with GetIt and Injectable
- **Local Storage** with Hive for offline support
- **Network Layer** with Dio and Retrofit
- **Code Generation** for models, repositories, and DI
- **Routing** with GoRouter for type-safe navigation
- **Theming** with Material Design 3 support
- **Error Handling** with comprehensive failure types

## 🛠️ Tech Stack

### Core Dependencies
- **Flutter SDK** - Cross-platform UI framework
- **Dart** - Programming language

### State Management
- **flutter_bloc** - Predictable state management
- **equatable** - Value equality for Dart classes

### Dependency Injection
- **get_it** - Service locator
- **injectable** - Code generation for DI

### Navigation
- **go_router** - Declarative routing

### Network & Data
- **dio** - HTTP client
- **retrofit** - Type-safe HTTP client
- **hive** - Lightweight local database
- **shared_preferences** - Simple key-value storage

### Code Generation
- **freezed** - Immutable classes and unions
- **json_annotation** - JSON serialization
- **build_runner** - Code generation runner

### UI & Utilities
- **flutter_svg** - SVG support
- **cached_network_image** - Image caching
- **intl** - Internationalization

## 📱 Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Android Studio / VS Code
- iOS development setup (for iOS builds)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd fam
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   dart run build_runner build
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

### Development Workflow

1. **Adding new features**: Follow the established feature structure
2. **Code generation**: Run `dart run build_runner build` after model changes
3. **Testing**: Write unit tests for business logic and widget tests for UI
4. **State management**: Use BLoC pattern for complex state, simple setState for simple UI state

## 🏛️ Architecture Principles

### Clean Architecture
- **Separation of Concerns**: Each layer has a single responsibility
- **Dependency Inversion**: High-level modules don't depend on low-level modules
- **Testability**: Business logic is independent of UI and external dependencies

### Feature-Based Organization
- **Scalability**: Easy to add new features without affecting existing ones
- **Team Collaboration**: Multiple developers can work on different features
- **Code Reusability**: Shared components in the core module

### State Management
- **BLoC Pattern**: Predictable state management with clear separation of business logic
- **Event-Driven**: UI events trigger business logic through well-defined events
- **Reactive**: UI rebuilds automatically when state changes

## 🧪 Testing Strategy

### Unit Tests
- Test business logic in use cases
- Test data transformations in repositories
- Test state management in BLoCs

### Widget Tests
- Test UI components in isolation
- Test user interactions and state changes
- Test navigation flows

### Integration Tests
- Test complete user journeys
- Test API integrations
- Test local storage operations

## 📦 Build & Deployment

### Development Build
```bash
flutter run --debug
```

### Release Build
```bash
flutter build apk --release          # Android
flutter build ios --release          # iOS
```

### Code Generation
```bash
dart run build_runner build --delete-conflicting-outputs
```

## 🤝 Contributing

1. Follow the established architecture patterns
2. Write tests for new features
3. Update documentation for significant changes
4. Use conventional commit messages
5. Ensure code generation is up to date

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔗 Resources

- [Flutter Documentation](https://docs.flutter.dev/)
- [BLoC Library](https://bloclibrary.dev/)
- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [Material Design 3](https://m3.material.io/)
