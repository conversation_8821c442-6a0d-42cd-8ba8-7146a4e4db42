# Development Guide

This guide provides detailed instructions for developing and extending the Family Manager application.

## 🏗️ Adding New Features

### 1. Create Feature Structure

When adding a new feature, follow this structure:

```
lib/features/new_feature/
├── data/
│   ├── datasources/
│   │   ├── new_feature_local_datasource.dart
│   │   └── new_feature_remote_datasource.dart
│   ├── models/
│   │   └── new_feature_model.dart
│   └── repositories/
│       └── new_feature_repository_impl.dart
├── domain/
│   ├── entities/
│   │   └── new_feature.dart
│   ├── repositories/
│   │   └── new_feature_repository.dart
│   └── usecases/
│       ├── get_new_feature.dart
│       ├── create_new_feature.dart
│       └── update_new_feature.dart
└── presentation/
    ├── bloc/
    │   ├── new_feature_bloc.dart
    │   ├── new_feature_event.dart
    │   └── new_feature_state.dart
    ├── pages/
    │   ├── new_feature_page.dart
    │   └── new_feature_detail_page.dart
    └── widgets/
        └── new_feature_card.dart
```

### 2. Domain Layer (Business Logic)

#### Entity
```dart
// lib/features/new_feature/domain/entities/new_feature.dart
import 'package:equatable/equatable.dart';

class NewFeature extends Equatable {
  final String id;
  final String name;
  final DateTime createdAt;

  const NewFeature({
    required this.id,
    required this.name,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [id, name, createdAt];
}
```

#### Repository Interface
```dart
// lib/features/new_feature/domain/repositories/new_feature_repository.dart
import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/new_feature.dart';

abstract class NewFeatureRepository {
  Future<Either<Failure, List<NewFeature>>> getNewFeatures();
  Future<Either<Failure, NewFeature>> createNewFeature(NewFeature feature);
}
```

#### Use Case
```dart
// lib/features/new_feature/domain/usecases/get_new_features.dart
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/new_feature.dart';
import '../repositories/new_feature_repository.dart';

@injectable
class GetNewFeatures implements NoParamsUseCase<List<NewFeature>> {
  final NewFeatureRepository repository;

  const GetNewFeatures(this.repository);

  @override
  Future<Either<Failure, List<NewFeature>>> call() async {
    return await repository.getNewFeatures();
  }
}
```

### 3. Data Layer

#### Model
```dart
// lib/features/new_feature/data/models/new_feature_model.dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';
import '../../domain/entities/new_feature.dart';

part 'new_feature_model.freezed.dart';
part 'new_feature_model.g.dart';

@freezed
@HiveType(typeId: 10) // Use unique typeId
class NewFeatureModel with _$NewFeatureModel {
  const factory NewFeatureModel({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required DateTime createdAt,
  }) = _NewFeatureModel;

  factory NewFeatureModel.fromJson(Map<String, dynamic> json) =>
      _$NewFeatureModelFromJson(json);

  const NewFeatureModel._();

  NewFeature toDomain() {
    return NewFeature(
      id: id,
      name: name,
      createdAt: createdAt,
    );
  }

  factory NewFeatureModel.fromDomain(NewFeature feature) {
    return NewFeatureModel(
      id: feature.id,
      name: feature.name,
      createdAt: feature.createdAt,
    );
  }
}
```

#### Repository Implementation
```dart
// lib/features/new_feature/data/repositories/new_feature_repository_impl.dart
import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/new_feature.dart';
import '../../domain/repositories/new_feature_repository.dart';
import '../datasources/new_feature_local_datasource.dart';
import '../datasources/new_feature_remote_datasource.dart';

@LazySingleton(as: NewFeatureRepository)
class NewFeatureRepositoryImpl implements NewFeatureRepository {
  final NewFeatureRemoteDataSource remoteDataSource;
  final NewFeatureLocalDataSource localDataSource;

  const NewFeatureRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, List<NewFeature>>> getNewFeatures() async {
    try {
      final remoteFeatures = await remoteDataSource.getNewFeatures();
      final features = remoteFeatures.map((model) => model.toDomain()).toList();
      
      // Cache locally
      await localDataSource.cacheNewFeatures(remoteFeatures);
      
      return Right(features);
    } on ServerException catch (e) {
      // Try local cache
      try {
        final localFeatures = await localDataSource.getNewFeatures();
        final features = localFeatures.map((model) => model.toDomain()).toList();
        return Right(features);
      } on CacheException {
        return Left(ServerFailure(message: e.message, code: e.code));
      }
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }
}
```

### 4. Presentation Layer

#### BLoC Events
```dart
// lib/features/new_feature/presentation/bloc/new_feature_event.dart
import 'package:equatable/equatable.dart';

abstract class NewFeatureEvent extends Equatable {
  const NewFeatureEvent();

  @override
  List<Object?> get props => [];
}

class LoadNewFeatures extends NewFeatureEvent {
  const LoadNewFeatures();
}
```

#### BLoC States
```dart
// lib/features/new_feature/presentation/bloc/new_feature_state.dart
import 'package:equatable/equatable.dart';
import '../../domain/entities/new_feature.dart';

abstract class NewFeatureState extends Equatable {
  const NewFeatureState();

  @override
  List<Object?> get props => [];
}

class NewFeatureInitial extends NewFeatureState {
  const NewFeatureInitial();
}

class NewFeatureLoading extends NewFeatureState {
  const NewFeatureLoading();
}

class NewFeatureLoaded extends NewFeatureState {
  final List<NewFeature> features;

  const NewFeatureLoaded(this.features);

  @override
  List<Object?> get props => [features];
}

class NewFeatureError extends NewFeatureState {
  final String message;

  const NewFeatureError(this.message);

  @override
  List<Object?> get props => [message];
}
```

#### BLoC
```dart
// lib/features/new_feature/presentation/bloc/new_feature_bloc.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../../domain/usecases/get_new_features.dart';
import 'new_feature_event.dart';
import 'new_feature_state.dart';

@injectable
class NewFeatureBloc extends Bloc<NewFeatureEvent, NewFeatureState> {
  final GetNewFeatures getNewFeatures;

  NewFeatureBloc({
    required this.getNewFeatures,
  }) : super(const NewFeatureInitial()) {
    on<LoadNewFeatures>(_onLoadNewFeatures);
  }

  Future<void> _onLoadNewFeatures(
    LoadNewFeatures event,
    Emitter<NewFeatureState> emit,
  ) async {
    emit(const NewFeatureLoading());

    final result = await getNewFeatures();

    result.fold(
      (failure) => emit(NewFeatureError(failure.message)),
      (features) => emit(NewFeatureLoaded(features)),
    );
  }
}
```

## 🔧 Code Generation

After creating models or updating dependency injection:

```bash
# Generate code for models, repositories, and DI
dart run build_runner build

# Clean and regenerate (if conflicts)
dart run build_runner build --delete-conflicting-outputs

# Watch for changes during development
dart run build_runner watch
```

## 🧪 Testing

### Unit Tests
```dart
// test/features/new_feature/domain/usecases/get_new_features_test.dart
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

void main() {
  group('GetNewFeatures', () {
    test('should return list of features when repository call is successful', () async {
      // Arrange
      final mockRepository = MockNewFeatureRepository();
      final usecase = GetNewFeatures(mockRepository);
      final features = [/* test data */];
      
      when(mockRepository.getNewFeatures())
          .thenAnswer((_) async => Right(features));

      // Act
      final result = await usecase();

      // Assert
      expect(result, Right(features));
      verify(mockRepository.getNewFeatures());
    });
  });
}
```

### Widget Tests
```dart
// test/features/new_feature/presentation/pages/new_feature_page_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('NewFeaturePage', () {
    testWidgets('should display loading indicator when loading', (tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: NewFeaturePage(),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
  });
}
```

## 🎨 UI Guidelines

### Consistent Styling
- Use theme colors from `AppColors`
- Use text styles from `AppTextStyles`
- Follow Material Design 3 guidelines

### Shared Widgets
- Use `CustomAppBar` for consistent app bars
- Use `LoadingWidget` for loading states
- Use `CustomErrorWidget` for error states
- Use `EmptyStateWidget` for empty states

### Navigation
```dart
// Navigate to a page
context.go('/new-feature');

// Navigate with parameters
context.go('/new-feature/123');

// Navigate and replace
context.pushReplacement('/new-feature');
```

## 📱 Platform-Specific Considerations

### Android
- Minimum SDK: 21 (Android 5.0)
- Target SDK: Latest stable
- Use Material Design components

### iOS
- Minimum iOS: 12.0
- Use Cupertino widgets where appropriate
- Follow iOS Human Interface Guidelines

## 🔍 Debugging

### Common Issues
1. **Code generation errors**: Run `flutter clean` then `flutter pub get`
2. **Dependency injection errors**: Check `@injectable` annotations
3. **Navigation errors**: Verify route definitions in `AppRouter`

### Debugging Tools
- Flutter Inspector for UI debugging
- Dart DevTools for performance analysis
- BLoC Inspector for state management debugging

## 📦 Dependencies Management

### Adding New Dependencies
1. Add to `pubspec.yaml`
2. Run `flutter pub get`
3. Update documentation if needed

### Updating Dependencies
```bash
flutter pub upgrade
```

### Dependency Guidelines
- Prefer stable, well-maintained packages
- Check package scores on pub.dev
- Consider bundle size impact
- Ensure license compatibility

## 🚀 Performance Optimization

### Best Practices
- Use `const` constructors where possible
- Implement proper `build` method optimization
- Use `ListView.builder` for large lists
- Optimize image loading with `cached_network_image`
- Implement proper state management to avoid unnecessary rebuilds

### Memory Management
- Dispose controllers and streams properly
- Use weak references where appropriate
- Monitor memory usage with DevTools

## 📋 Code Review Checklist

- [ ] Follows established architecture patterns
- [ ] Includes appropriate tests
- [ ] Uses consistent naming conventions
- [ ] Implements proper error handling
- [ ] Includes documentation for complex logic
- [ ] Follows Dart/Flutter best practices
- [ ] Code generation is up to date
- [ ] No hardcoded strings (use constants)
- [ ] Proper null safety implementation
- [ ] Performance considerations addressed
