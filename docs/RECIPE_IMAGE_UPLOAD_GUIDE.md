# Recipe Image Upload Implementation Guide

## Overview

This guide explains how to properly upload recipe images to Supabase storage and use them in the database. The implementation includes:

1. **Proper image upload to Supabase storage**
2. **Recipe creation with image selection**
3. **Sample image upload utility**
4. **Database integration with storage URLs**

## Features Implemented

### 1. Recipe Creation Screen
- **Location**: `lib/features/recipes/screens/create_recipe_screen.dart`
- **Features**:
  - Image picker for multiple recipe photos
  - Form validation for all recipe fields
  - Category and difficulty selection
  - Ingredients and instructions input
  - Tags support
  - Real-time image upload with progress

### 2. Image Upload Service
- **Location**: `lib/features/recipes/domain/repository/supabase_recipe_repository.dart`
- **Method**: `uploadRecipeImage(String imagePath)`
- **Features**:
  - Uploads images to `recipe-images` bucket
  - Generates unique filenames with timestamps
  - Returns public URLs for database storage

### 3. Sample Image Upload Utility
- **Location**: `lib/features/recipes/utils/upload_sample_images.dart`
- **Purpose**: Upload sample food images from Unsplash to Supabase storage
- **Features**:
  - Downloads high-quality food images
  - Uploads to Supabase storage
  - Updates existing recipes with storage URLs

### 4. Upload Images Screen (Development Tool)
- **Location**: `lib/features/recipes/screens/upload_images_screen.dart`
- **Purpose**: Debug screen to upload sample images
- **Access**: Cloud upload button in recipes screen header

## How to Use

### For Users (Recipe Creation)

1. **Navigate to Recipes Screen**
2. **Tap "Add Recipe" button** (floating action button)
3. **Fill in recipe details**:
   - Add at least one recipe image
   - Enter title and description
   - Select category and difficulty
   - Add preparation/cooking time and servings
   - List ingredients (one per line)
   - Write step-by-step instructions
   - Add optional tags
4. **Tap "Create Recipe"** to save

### For Developers (Sample Images)

1. **Access Upload Screen**: Tap the cloud upload icon in recipes screen header
2. **Upload Sample Images**: Tap "Upload Sample Images" button
3. **Monitor Progress**: Watch the logs for upload status
4. **Verify Results**: Check recipes screen for updated images

## Technical Implementation

### Image Upload Flow

```dart
// 1. User selects images
final List<XFile> images = await _imagePicker.pickMultiImage();

// 2. Upload each image to Supabase storage
for (File imageFile in imageFiles) {
  final imageUrl = await _recipeService.uploadRecipeImage(imageFile.path);
  imageUrls.add(imageUrl);
}

// 3. Create recipe with image URLs
final recipe = await _recipeService.createRecipe(
  CreateRecipeRequest(
    // ... other fields
    imageUrls: imageUrls,
  ),
);
```

### Storage Configuration

- **Bucket Name**: `recipe-images`
- **File Naming**: `recipe_[timestamp].[extension]`
- **Public Access**: URLs are publicly accessible
- **File Types**: JPG, PNG supported

### Database Schema

```sql
-- Recipes table includes image_urls column
CREATE TABLE recipes (
  id UUID PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  image_urls TEXT[] DEFAULT '{}', -- Array of storage URLs
  -- ... other columns
);
```

## Troubleshooting

### Common Issues

1. **Storage Bucket Not Accessible**
   - Verify `recipe-images` bucket exists in Supabase dashboard
   - Check bucket permissions and RLS policies
   - Ensure bucket is public or has proper access policies

2. **Image Upload Fails**
   - Check file size (should be < 50MB)
   - Verify file format (JPG, PNG)
   - Check network connectivity
   - Verify Supabase credentials

3. **Images Not Displaying**
   - Verify URLs are properly stored in database
   - Check if URLs are publicly accessible
   - Ensure image URLs are in correct format

### Debug Steps

1. **Test Storage Access**:
   ```dart
   final accessible = await UploadSampleImages.checkStorageBucket();
   print('Bucket accessible: $accessible');
   ```

2. **Upload Sample Images**:
   - Use the upload images screen
   - Monitor logs for errors
   - Check Supabase dashboard for uploaded files

3. **Verify Database Updates**:
   - Check recipes table in Supabase dashboard
   - Verify `image_urls` column contains storage URLs
   - Test image display in app

## File Structure

```
lib/features/recipes/
├── screens/
│   ├── create_recipe_screen.dart      # Recipe creation with image upload
│   ├── upload_images_screen.dart      # Sample image upload utility
│   └── recipes_screen.dart            # Main recipes screen with FAB
├── logic/
│   └── recipe_creation/
│       ├── recipe_creation_cubit.dart # Recipe creation state management
│       └── recipe_creation_state.dart # Recipe creation states
├── domain/
│   ├── models/
│   │   └── recipe_model.dart          # Includes CreateRecipeRequest
│   ├── repository/
│   │   └── supabase_recipe_repository.dart # Image upload implementation
│   └── services/
│       └── recipe_service.dart        # Recipe service layer
└── utils/
    ├── image_upload_helper.dart       # Image upload utilities
    └── upload_sample_images.dart      # Sample image upload logic
```

## Next Steps

1. **Test the implementation** by creating a new recipe with images
2. **Upload sample images** using the debug screen
3. **Verify storage URLs** in the database
4. **Check image display** in recipe cards and detail screens

The implementation provides a complete solution for recipe image upload with proper error handling, progress tracking, and database integration.
