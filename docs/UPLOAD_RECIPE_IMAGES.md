# How to Upload Recipe Images to Supabase Storage

## Method 1: Using Supabase Dashboard (Recommended)

1. **Go to Supabase Dashboard**
   - Visit: https://supabase.com/dashboard
   - Navigate to your project: `fam` (ovvathmpvaeeesmiywjb)

2. **Access Storage**
   - Click on "Storage" in the left sidebar
   - Select the `recipe-images` bucket

3. **Upload Images**
   - Click "Upload file" button
   - Upload these food images with these exact names:
     - `kerala-fish-curry.jpg` - Kerala Fish Curry image
     - `chicken-biryani.jpg` - Chicken Biryani image
     - `masala-dosa.jpg` - Masala <PERSON> image
     - `butter-chicken.jpg` - Butter Chicken image
     - `mango-lassi.jpg` - Mango Lassi image
     - `chocolate-cookies.jpg` - Chocolate Chip Cookies image
     - `appam-stew.jpg` - Appam with Stew image
     - `puttu-kadala.jpg` - Puttu and Kadala Curry image
     - `sambar-rice.jpg` - Sambar Rice image
     - `avocado-toast.jpg` - Avocado Toast image
     - `payasam.jpg` - Payasam image
     - `quinoa-salad.jpg` - Quinoa Salad Bowl image
     - `masala-chai.jpg` - Masala Chai image
     - `fish-molee.jpg` - Fish Molee image

4. **Get Image URLs**
   After uploading, the URLs will be:
   ```
   https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/kerala-fish-curry.jpg
   https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/chicken-biryani.jpg
   ... and so on
   ```

## Method 2: Using SQL Update (Temporary Fix)

Run this SQL in Supabase SQL Editor to use working placeholder images:

```sql
-- Update with working placeholder images
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/ff6b6b/ffffff&text=Kerala+Fish+Curry'] WHERE title = 'Kerala Fish Curry';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/4ecdc4/ffffff&text=Chicken+Biriyani'] WHERE title = 'Chicken Biriyani';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/45b7d1/ffffff&text=Masala+Dosa'] WHERE title = 'Masala Dosa';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/f9ca24/ffffff&text=Butter+Chicken'] WHERE title = 'Butter Chicken';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/f0932b/ffffff&text=Mango+Lassi'] WHERE title = 'Mango Lassi';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/eb4d4b/ffffff&text=Chocolate+Cookies'] WHERE title = 'Chocolate Chip Cookies';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/6c5ce7/ffffff&text=Appam+with+Stew'] WHERE title = 'Appam with Stew';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/a29bfe/ffffff&text=Puttu+Kadala'] WHERE title = 'Puttu and Kadala Curry';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/fd79a8/ffffff&text=Sambar+Rice'] WHERE title = 'Sambar Rice';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/00b894/ffffff&text=Avocado+Toast'] WHERE title = 'Avocado Toast';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/fdcb6e/ffffff&text=Payasam'] WHERE title = 'Payasam';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/e17055/ffffff&text=Quinoa+Salad'] WHERE title = 'Quinoa Salad Bowl';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/81ecec/ffffff&text=Masala+Chai'] WHERE title = 'Masala Chai';
UPDATE recipes SET image_urls = ARRAY['https://dummyimage.com/800x600/74b9ff/ffffff&text=Fish+Molee'] WHERE title = 'Fish Molee';

-- Update author images
UPDATE recipes SET author_image_url = 'https://dummyimage.com/150x150/2d3436/ffffff&text=' || SUBSTRING(author_name, 1, 1);
UPDATE recipe_comments SET author_image_url = 'https://dummyimage.com/150x150/636e72/ffffff&text=' || SUBSTRING(author_name, 1, 1);
```

## Method 3: Update Database with Supabase Storage URLs

After uploading images to storage, run this SQL:

```sql
-- Update recipes with Supabase storage URLs
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/kerala-fish-curry.jpg'] WHERE title = 'Kerala Fish Curry';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/chicken-biryani.jpg'] WHERE title = 'Chicken Biriyani';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/masala-dosa.jpg'] WHERE title = 'Masala Dosa';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/butter-chicken.jpg'] WHERE title = 'Butter Chicken';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/mango-lassi.jpg'] WHERE title = 'Mango Lassi';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/chocolate-cookies.jpg'] WHERE title = 'Chocolate Chip Cookies';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/appam-stew.jpg'] WHERE title = 'Appam with Stew';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/puttu-kadala.jpg'] WHERE title = 'Puttu and Kadala Curry';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/sambar-rice.jpg'] WHERE title = 'Sambar Rice';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/avocado-toast.jpg'] WHERE title = 'Avocado Toast';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/payasam.jpg'] WHERE title = 'Payasam';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/quinoa-salad.jpg'] WHERE title = 'Quinoa Salad Bowl';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/masala-chai.jpg'] WHERE title = 'Masala Chai';
UPDATE recipes SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/fish-molee.jpg'] WHERE title = 'Fish Molee';
```

## Recommended Food Images Sources

1. **Unsplash** (free): https://unsplash.com/s/photos/indian-food
2. **Pexels** (free): https://www.pexels.com/search/indian%20food/
3. **Pixabay** (free): https://pixabay.com/images/search/indian%20food/

## Notes

- Images should be in JPG format
- Recommended size: 800x600 pixels
- Keep file sizes under 1MB for better performance
- Use descriptive filenames matching the recipe names
