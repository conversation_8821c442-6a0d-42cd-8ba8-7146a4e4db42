import 'dart:typed_data';
import 'package:http/http.dart' as http;

/// Script to upload recipe images to Supabase storage
/// This script downloads food images and uploads them to Supabase storage
class RecipeImageUploader {
  static const String supabaseUrl = 'https://ovvathmpvaeeesmiywjb.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92dmF0aG1wdmFlZWVzbWl5d2piIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NDI3NjUsImV4cCI6MjA2NDQxODc2NX0.MYSVq47Zx0riqlptDGVTDP0QEX1lhecmdaQ56flZ-n4';

  /// Upload a file to Supabase storage using REST API
  static Future<String> uploadImageToStorage(String fileName, Uint8List imageBytes) async {
    try {
      final url = Uri.parse('$supabaseUrl/storage/v1/object/recipe-images/$fileName');

      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'Content-Type': 'image/jpeg',
          'x-upsert': 'true', // Allow overwriting existing files
        },
        body: imageBytes,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Return the public URL
        return '$supabaseUrl/storage/v1/object/public/recipe-images/$fileName';
      } else {
        throw Exception('Failed to upload image: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Error uploading image: $e');
    }
  }

  /// Download an image from URL
  static Future<Uint8List> downloadImage(String imageUrl) async {
    try {
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        return response.bodyBytes;
      } else {
        throw Exception('Failed to download image: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error downloading image: $e');
    }
  }

  /// Download images and upload to Supabase storage, then update database
  static Future<void> downloadAndUploadImagesToStorage() async {
    // High-quality food image URLs from Pexels
    final recipeImageUrls = {
      'Kerala Fish Curry': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Chicken Biriyani': [
        'https://images.pexels.com/photos/2474658/pexels-photo-2474658.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Masala Dosa': [
        'https://images.pexels.com/photos/5560763/pexels-photo-5560763.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Butter Chicken': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Mango Lassi': [
        'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Chocolate Chip Cookies': [
        'https://images.pexels.com/photos/230325/pexels-photo-230325.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Appam with Stew': [
        'https://images.pexels.com/photos/5560763/pexels-photo-5560763.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Puttu and Kadala Curry': [
        'https://images.pexels.com/photos/2474658/pexels-photo-2474658.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Sambar Rice': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Avocado Toast': [
        'https://images.pexels.com/photos/566566/pexels-photo-566566.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Payasam': [
        'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Quinoa Salad Bowl': [
        'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Masala Chai': [
        'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Fish Molee': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
    };

    print('🚀 Starting download and upload process...');
    print('📊 Total recipes to process: ${recipeImageUrls.length}');

    final Map<String, List<String>> uploadedStorageUrls = {};
    int processedCount = 0;

    for (final entry in recipeImageUrls.entries) {
      final recipeName = entry.key;
      final imageUrls = entry.value;

      processedCount++;
      print('\n📝 Processing $recipeName ($processedCount/${recipeImageUrls.length})...');

      final List<String> storageUrls = [];

      for (int i = 0; i < imageUrls.length; i++) {
        final imageUrl = imageUrls[i];

        try {
          print('  📥 Downloading image ${i + 1}/${imageUrls.length} from Pexels...');
          final imageBytes = await downloadImage(imageUrl);

          // Generate filename based on recipe name
          final fileName = '${recipeName.toLowerCase().replaceAll(' ', '-').replaceAll('\'', '')}-${i + 1}.jpg';

          print('  📤 Uploading $fileName to Supabase storage...');
          final storageUrl = await uploadImageToStorage(fileName, imageBytes);

          storageUrls.add(storageUrl);
          print('  ✅ Successfully uploaded: $fileName');

          // Add delay to avoid rate limiting
          await Future.delayed(const Duration(milliseconds: 500));

        } catch (e) {
          print('  ❌ Error processing image ${i + 1} for $recipeName: $e');
        }
      }

      if (storageUrls.isNotEmpty) {
        uploadedStorageUrls[recipeName] = storageUrls;
        print('  🎉 Completed $recipeName: ${storageUrls.length} images uploaded');
      } else {
        print('  ⚠️  No images uploaded for $recipeName');
      }
    }

    print('\n🔄 Updating database with storage URLs...');
    await updateDatabaseWithStorageUrls(uploadedStorageUrls);

    print('\n🎊 Process completed!');
    print('📈 Summary:');
    print('   - Total recipes processed: ${recipeImageUrls.length}');
    print('   - Recipes with uploaded images: ${uploadedStorageUrls.length}');
    print('   - Total images uploaded: ${uploadedStorageUrls.values.fold(0, (sum, urls) => sum + urls.length)}');
  }

  /// Update database with storage URLs
  static Future<void> updateDatabaseWithStorageUrls(Map<String, List<String>> storageUrls) async {
    for (final entry in storageUrls.entries) {
      final recipeName = entry.key;
      final imageUrls = entry.value;

      try {
        await updateRecipeImages(recipeName, imageUrls);
        print('✅ Updated database for $recipeName with ${imageUrls.length} storage URLs');
      } catch (e) {
        print('❌ Error updating database for $recipeName: $e');
      }
    }
  }

  /// Update database with working food image URLs (fallback method)
  static Future<void> updateDatabaseWithWorkingImages() async {
    // Use reliable food image URLs that will definitely work
    final recipeImageUrls = {
      'Kerala Fish Curry': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Chicken Biriyani': [
        'https://images.pexels.com/photos/2474658/pexels-photo-2474658.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Masala Dosa': [
        'https://images.pexels.com/photos/5560763/pexels-photo-5560763.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Butter Chicken': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Mango Lassi': [
        'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Chocolate Chip Cookies': [
        'https://images.pexels.com/photos/230325/pexels-photo-230325.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Appam with Stew': [
        'https://images.pexels.com/photos/5560763/pexels-photo-5560763.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Puttu and Kadala Curry': [
        'https://images.pexels.com/photos/2474658/pexels-photo-2474658.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Sambar Rice': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Avocado Toast': [
        'https://images.pexels.com/photos/566566/pexels-photo-566566.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Payasam': [
        'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Quinoa Salad Bowl': [
        'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Masala Chai': [
        'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Fish Molee': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
    };

    print('Updating database with working food image URLs...');

    for (final entry in recipeImageUrls.entries) {
      final recipeName = entry.key;
      final imageUrls = entry.value;

      try {
        await updateRecipeImages(recipeName, imageUrls);
        print('✅ Updated $recipeName with ${imageUrls.length} images');
      } catch (e) {
        print('❌ Error updating $recipeName: $e');
      }
    }

    print('Database update completed!');
  }

  /// Update a specific recipe with new image URLs using REST API
  static Future<void> updateRecipeImages(String recipeName, List<String> imageUrls) async {
    try {
      final url = Uri.parse('$supabaseUrl/rest/v1/recipes?title=eq.$recipeName');

      final response = await http.patch(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Prefer': 'return=minimal',
        },
        body: '{"image_urls": ${imageUrls.map((url) => '"$url"').toList()}}',
      );

      if (response.statusCode != 204) {
        throw Exception('Failed to update recipe: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Error updating recipe images: $e');
    }
  }

  /// Force upload images without checking bucket (since you confirmed it exists)
  static Future<void> forceUploadImages() async {
    // High-quality food image URLs from Pexels
    final recipeImageUrls = {
      'Kerala Fish Curry': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Chicken Biriyani': [
        'https://images.pexels.com/photos/2474658/pexels-photo-2474658.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Masala Dosa': [
        'https://images.pexels.com/photos/5560763/pexels-photo-5560763.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Butter Chicken': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Mango Lassi': [
        'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
    };

    print('💪 Force uploading images (skipping bucket check)...');
    print('📊 Total recipes to process: ${recipeImageUrls.length}');

    final Map<String, List<String>> uploadedStorageUrls = {};
    int processedCount = 0;

    for (final entry in recipeImageUrls.entries) {
      final recipeName = entry.key;
      final imageUrls = entry.value;

      processedCount++;
      print('\n📝 Processing $recipeName ($processedCount/${recipeImageUrls.length})...');

      final List<String> storageUrls = [];

      for (int i = 0; i < imageUrls.length; i++) {
        final imageUrl = imageUrls[i];

        try {
          print('  📥 Downloading image ${i + 1}/${imageUrls.length} from Pexels...');
          final imageBytes = await downloadImage(imageUrl);

          // Generate filename based on recipe name
          final fileName = '${recipeName.toLowerCase().replaceAll(' ', '-').replaceAll('\'', '')}-${i + 1}.jpg';

          print('  📤 Uploading $fileName to Supabase storage...');
          final storageUrl = await uploadImageToStorage(fileName, imageBytes);

          storageUrls.add(storageUrl);
          print('  ✅ Successfully uploaded: $fileName');

          // Add delay to avoid rate limiting
          await Future.delayed(const Duration(milliseconds: 500));

        } catch (e) {
          print('  ❌ Error processing image ${i + 1} for $recipeName: $e');
        }
      }

      if (storageUrls.isNotEmpty) {
        uploadedStorageUrls[recipeName] = storageUrls;
        print('  🎉 Completed $recipeName: ${storageUrls.length} images uploaded');
      } else {
        print('  ⚠️  No images uploaded for $recipeName');
      }
    }

    print('\n🔄 Updating database with storage URLs...');
    await updateDatabaseWithStorageUrls(uploadedStorageUrls);

    print('\n🎊 Force upload completed!');
    print('📈 Summary:');
    print('   - Total recipes processed: ${recipeImageUrls.length}');
    print('   - Recipes with uploaded images: ${uploadedStorageUrls.length}');
    print('   - Total images uploaded: ${uploadedStorageUrls.values.fold(0, (sum, urls) => sum + urls.length)}');
  }

  /// Check if storage bucket is accessible by trying to list files
  static Future<bool> checkStorageBucket() async {
    try {
      // Try to list files in the bucket - this is a better test
      final url = Uri.parse('$supabaseUrl/storage/v1/object/list/recipe-images');
      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
        },
        body: '{"limit": 1, "prefix": ""}',
      );

      print('Storage check response: ${response.statusCode}');
      if (response.statusCode != 200) {
        print('Response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        print('✅ Storage bucket "recipe-images" is accessible');
        return true;
      } else {
        print('❌ Storage bucket not accessible: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('❌ Error checking storage bucket: $e');
      return false;
    }
  }

  /// List files in storage bucket
  static Future<void> listStorageFiles() async {
    try {
      final url = Uri.parse('$supabaseUrl/storage/v1/object/list/recipe-images');
      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
        },
        body: '{}',
      );

      if (response.statusCode == 200) {
        print('📁 Files in recipe-images bucket:');
        print(response.body);
      } else {
        print('❌ Failed to list files: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error listing files: $e');
    }
  }
}

/// Main function with options
void main(List<String> args) async {
  print('🍽️  Recipe Image Uploader');
  print('========================');

  // Check storage bucket first
  final bucketAccessible = await RecipeImageUploader.checkStorageBucket();
  if (!bucketAccessible) {
    print('\n❌ Cannot proceed - storage bucket not accessible');
    print('Please check:');
    print('1. Bucket "recipe-images" exists in Supabase dashboard');
    print('2. Bucket has proper permissions/RLS policies');
    print('3. Your Supabase credentials are correct');
    return;
  }

  // Parse command line arguments
  final String action = args.isNotEmpty ? args[0] : 'upload';

  switch (action) {
    case 'upload':
      print('\n🚀 Starting download and upload to storage...');
      await RecipeImageUploader.downloadAndUploadImagesToStorage();
      break;

    case 'force':
      print('\n💪 Force upload (skipping bucket check)...');
      await RecipeImageUploader.forceUploadImages();
      break;

    case 'direct':
      print('\n🔗 Updating database with direct Pexels URLs...');
      await RecipeImageUploader.updateDatabaseWithWorkingImages();
      break;

    case 'list':
      print('\n📁 Listing storage files...');
      await RecipeImageUploader.listStorageFiles();
      break;

    case 'check':
      print('\n✅ Storage bucket check completed');
      break;

    default:
      print('\n❓ Usage:');
      print('  dart scripts/upload_recipe_images.dart [action]');
      print('');
      print('Actions:');
      print('  upload  - Download images and upload to Supabase storage (default)');
      print('  force   - Force upload without bucket check (use if bucket exists)');
      print('  direct  - Update database with direct Pexels URLs');
      print('  list    - List files in storage bucket');
      print('  check   - Check storage bucket accessibility');
      break;
  }

  print('\n✨ Script completed!');
}