import 'dart:typed_data';
import 'package:http/http.dart' as http;

/// Script to upload recipe images to Supabase storage
/// This script downloads food images and uploads them to Supabase storage
class RecipeImageUploader {
  static const String supabaseUrl = 'https://ovvathmpvaeeesmiywjb.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92dmF0aG1wdmFlZWVzbWl5d2piIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTcyOTc4MTYsImV4cCI6MjAzMjg3MzgxNn0.Ej5u3xJNkQOJQOQOQOQOQOQOQOQOQOQOQOQOQOQOQOQ';

  /// Upload a file to Supabase storage using REST API
  static Future<String> uploadImageToStorage(String fileName, Uint8List imageBytes) async {
    try {
      final url = Uri.parse('$supabaseUrl/storage/v1/object/recipe-images/$fileName');

      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'Content-Type': 'image/jpeg',
          'x-upsert': 'true', // Allow overwriting existing files
        },
        body: imageBytes,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Return the public URL
        return '$supabaseUrl/storage/v1/object/public/recipe-images/$fileName';
      } else {
        throw Exception('Failed to upload image: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Error uploading image: $e');
    }
  }

  /// Download an image from URL
  static Future<Uint8List> downloadImage(String imageUrl) async {
    try {
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        return response.bodyBytes;
      } else {
        throw Exception('Failed to download image: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error downloading image: $e');
    }
  }

  /// Update database with working food image URLs
  static Future<void> updateDatabaseWithWorkingImages() async {
    // Use reliable food image URLs that will definitely work
    final recipeImageUrls = {
      'Kerala Fish Curry': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Chicken Biriyani': [
        'https://images.pexels.com/photos/2474658/pexels-photo-2474658.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Masala Dosa': [
        'https://images.pexels.com/photos/5560763/pexels-photo-5560763.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Butter Chicken': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Mango Lassi': [
        'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Chocolate Chip Cookies': [
        'https://images.pexels.com/photos/230325/pexels-photo-230325.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Appam with Stew': [
        'https://images.pexels.com/photos/5560763/pexels-photo-5560763.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Puttu and Kadala Curry': [
        'https://images.pexels.com/photos/2474658/pexels-photo-2474658.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Sambar Rice': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Avocado Toast': [
        'https://images.pexels.com/photos/566566/pexels-photo-566566.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Payasam': [
        'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Quinoa Salad Bowl': [
        'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Masala Chai': [
        'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      'Fish Molee': [
        'https://images.pexels.com/photos/2474661/pexels-photo-2474661.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
    };

    print('Updating database with working food image URLs...');

    for (final entry in recipeImageUrls.entries) {
      final recipeName = entry.key;
      final imageUrls = entry.value;

      try {
        await updateRecipeImages(recipeName, imageUrls);
        print('✅ Updated $recipeName with ${imageUrls.length} images');
      } catch (e) {
        print('❌ Error updating $recipeName: $e');
      }
    }

    print('Database update completed!');
  }

  /// Update a specific recipe with new image URLs using REST API
  static Future<void> updateRecipeImages(String recipeName, List<String> imageUrls) async {
    try {
      final url = Uri.parse('$supabaseUrl/rest/v1/recipes?title=eq.$recipeName');

      final response = await http.patch(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Prefer': 'return=minimal',
        },
        body: '{"image_urls": ${imageUrls.map((url) => '"$url"').toList()}}',
      );

      if (response.statusCode != 204) {
        throw Exception('Failed to update recipe: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Error updating recipe images: $e');
    }
  }

}

void main() async {
  await RecipeImageUploader.updateDatabaseWithWorkingImages();
}