import 'dart:convert';
import 'package:http/http.dart' as http;

/// Script to fix recipe images by updating database with storage URLs
class RecipeImageFixer {
  static const String supabaseUrl = 'https://ovvathmpvaeeesmiywjb.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92dmF0aG1wdmFlZWVzbWl5d2piIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NDI3NjUsImV4cCI6MjA2NDQxODc2NX0.MYSVq47Zx0riqlptDGVTDP0QEX1lhecmdaQ56flZ-n4';

  /// Update all recipes with correct storage URLs
  static Future<void> fixAllRecipeImages() async {
    // Map of recipe titles to their storage filenames
    final Map<String, String> recipeToFile = {
      'Kerala Fish Curry': 'kerala-fish-curry-1.jpg',
      'Chicken Biriyani': 'chicken-biriyani-1.jpg',
      'Masala Dosa': 'masala-dosa-1.jpg',
      'Butter Chicken': 'butter-chicken-1.jpg',
      'Mango Lassi': 'mango-lassi-1.jpg',
      'Chocolate Chip Cookies': 'chocolate-chip-cookies-1.jpg',
      'Appam with Stew': 'appam-with-stew-1.jpg',
      'Puttu and Kadala Curry': 'puttu-and-kadala-curry-1.jpg',
      'Sambar Rice': 'sambar-rice-1.jpg',
      'Avocado Toast': 'avocado-toast-1.jpg',
      'Payasam': 'payasam-1.jpg',
      'Quinoa Salad Bowl': 'quinoa-salad-bowl-1.jpg',
      'Masala Chai': 'masala-chai-1.jpg',
      'Fish Molee': 'fish-molee-1.jpg',
    };

    print('🔧 Fixing recipe images...');
    print('===========================');

    int successCount = 0;
    int failCount = 0;

    for (final entry in recipeToFile.entries) {
      final recipeTitle = entry.key;
      final fileName = entry.value;
      final storageUrl = '$supabaseUrl/storage/v1/object/public/recipe-images/$fileName';

      try {
        print('\n📝 Updating: $recipeTitle');
        print('   📄 File: $fileName');
        print('   🔗 URL: $storageUrl');

        // First, test if the image is accessible
        final testResponse = await http.head(Uri.parse(storageUrl));
        if (testResponse.statusCode != 200) {
          print('   ❌ Image not accessible: ${testResponse.statusCode}');
          failCount++;
          continue;
        }

        // Update the recipe
        final updateUrl = Uri.parse('$supabaseUrl/rest/v1/recipes?title=eq.$recipeTitle');
        final response = await http.patch(
          updateUrl,
          headers: {
            'Authorization': 'Bearer $supabaseAnonKey',
            'Content-Type': 'application/json',
            'apikey': supabaseAnonKey,
            'Prefer': 'return=minimal',
          },
          body: json.encode({
            'image_urls': [storageUrl]
          }),
        );

        if (response.statusCode == 204) {
          print('   ✅ Successfully updated!');
          successCount++;
        } else {
          print('   ❌ Failed to update: ${response.statusCode}');
          print('   Response: ${response.body}');
          failCount++;
        }

        // Add small delay to avoid rate limiting
        await Future.delayed(const Duration(milliseconds: 200));

      } catch (e) {
        print('   ❌ Error updating $recipeTitle: $e');
        failCount++;
      }
    }

    print('\n🎊 Update completed!');
    print('📈 Summary:');
    print('   ✅ Successfully updated: $successCount recipes');
    print('   ❌ Failed: $failCount recipes');
    print('   📊 Total processed: ${recipeToFile.length} recipes');

    if (successCount > 0) {
      print('\n🚀 Your app should now show the real food images!');
    }
  }

  /// Test a single recipe update
  static Future<void> testSingleUpdate(String recipeTitle, String fileName) async {
    final storageUrl = '$supabaseUrl/storage/v1/object/public/recipe-images/$fileName';
    
    print('🧪 Testing update for: $recipeTitle');
    print('📄 File: $fileName');
    print('🔗 URL: $storageUrl');

    try {
      // Test image accessibility
      print('\n1️⃣ Testing image accessibility...');
      final testResponse = await http.head(Uri.parse(storageUrl));
      if (testResponse.statusCode == 200) {
        print('   ✅ Image is accessible');
        print('   📏 Size: ${testResponse.headers['content-length']} bytes');
        print('   🎨 Type: ${testResponse.headers['content-type']}');
      } else {
        print('   ❌ Image not accessible: ${testResponse.statusCode}');
        return;
      }

      // Update recipe
      print('\n2️⃣ Updating recipe in database...');
      final updateUrl = Uri.parse('$supabaseUrl/rest/v1/recipes?title=eq.$recipeTitle');
      final response = await http.patch(
        updateUrl,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Prefer': 'return=minimal',
        },
        body: json.encode({
          'image_urls': [storageUrl]
        }),
      );

      if (response.statusCode == 204) {
        print('   ✅ Recipe updated successfully!');
      } else {
        print('   ❌ Failed to update recipe: ${response.statusCode}');
        print('   Response: ${response.body}');
      }

    } catch (e) {
      print('❌ Error: $e');
    }
  }

  /// Verify all updates worked
  static Future<void> verifyUpdates() async {
    try {
      print('🔍 Verifying recipe updates...');
      print('==============================');

      final url = Uri.parse('$supabaseUrl/rest/v1/recipes');
      final response = await http.get(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'apikey': supabaseAnonKey,
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> recipes = json.decode(response.body);
        
        int storageUrlCount = 0;
        int dummyUrlCount = 0;

        for (final recipe in recipes) {
          final title = recipe['title'] ?? 'Unknown';
          final imageUrls = recipe['image_urls'] ?? [];
          
          if (imageUrls.isNotEmpty) {
            final firstUrl = imageUrls[0] as String;
            if (firstUrl.contains('supabase.co/storage')) {
              print('✅ $title - Using storage URL');
              storageUrlCount++;
            } else if (firstUrl.contains('dummyimage.com')) {
              print('❌ $title - Still using dummy URL');
              dummyUrlCount++;
            } else {
              print('⚠️  $title - Using other URL: $firstUrl');
            }
          } else {
            print('❌ $title - No images');
          }
        }

        print('\n📊 Verification Summary:');
        print('   ✅ Using storage URLs: $storageUrlCount');
        print('   ❌ Still using dummy URLs: $dummyUrlCount');
        print('   📱 Total recipes: ${recipes.length}');

        if (storageUrlCount == recipes.length) {
          print('\n🎉 All recipes are now using storage URLs!');
        } else if (dummyUrlCount > 0) {
          print('\n⚠️  Some recipes still need fixing.');
        }

      } else {
        print('❌ Failed to fetch recipes: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error verifying updates: $e');
    }
  }
}

void main(List<String> args) async {
  print('🔧 Recipe Image Fixer');
  print('=====================\n');

  final String action = args.isNotEmpty ? args[0] : 'fix';

  switch (action) {
    case 'fix':
      await RecipeImageFixer.fixAllRecipeImages();
      break;

    case 'test':
      if (args.length >= 3) {
        await RecipeImageFixer.testSingleUpdate(args[1], args[2]);
      } else {
        print('Usage: dart scripts/fix_recipe_images.dart test "Recipe Title" "filename.jpg"');
      }
      break;

    case 'verify':
      await RecipeImageFixer.verifyUpdates();
      break;

    default:
      print('Usage:');
      print('  dart scripts/fix_recipe_images.dart fix     - Fix all recipe images');
      print('  dart scripts/fix_recipe_images.dart verify  - Verify updates worked');
      print('  dart scripts/fix_recipe_images.dart test "Recipe Title" "filename.jpg"');
      break;
  }

  print('\n✨ Done!');
}
