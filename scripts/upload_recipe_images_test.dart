import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../lib/core/network/supabase_config.dart';
import '../lib/features/recipes/utils/upload_sample_images.dart';

/// Test script to upload sample recipe images to Supabase storage
/// Run this with: flutter run scripts/upload_recipe_images_test.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Supabase
  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );
  
  print('🔧 Supabase initialized');
  
  // Check if storage bucket is accessible
  final bucketAccessible = await UploadSampleImages.checkStorageBucket();
  
  if (!bucketAccessible) {
    print('❌ Cannot access recipe-images bucket. Please check:');
    print('   1. Bucket exists in Supabase dashboard');
    print('   2. Bucket is public or has proper RLS policies');
    print('   3. Your Supabase credentials are correct');
    return;
  }
  
  // Run the upload process
  await UploadSampleImages.uploadAndUpdateRecipes();
  
  print('✨ Script completed!');
}
