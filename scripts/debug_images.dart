import 'dart:convert';
import 'package:http/http.dart' as http;

/// Debug script to check current state of images
class ImageDebugger {
  static const String supabaseUrl = 'https://ovvathmpvaeeesmiywjb.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92dmF0aG1wdmFlZWVzbWl5d2piIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NDI3NjUsImV4cCI6MjA2NDQxODc2NX0.MYSVq47Zx0riqlptDGVTDP0QEX1lhecmdaQ56flZ-n4';

  /// Check what's in the database
  static Future<void> checkDatabase() async {
    try {
      print('🔍 Checking database recipes...');
      print('================================');
      
      final url = Uri.parse('$supabaseUrl/rest/v1/recipes');
      final response = await http.get(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'apikey': supabaseAnonKey,
          'Content-Type': 'application/json',
        },
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> recipes = json.decode(response.body);
        
        for (final recipe in recipes) {
          final title = recipe['title'] ?? 'Unknown';
          final imageUrls = recipe['image_urls'] ?? [];
          
          print('📝 Recipe: $title');
          if (imageUrls.isEmpty) {
            print('   ❌ No images');
          } else {
            for (int i = 0; i < imageUrls.length; i++) {
              print('   🖼️  Image ${i + 1}: ${imageUrls[i]}');
            }
          }
          print('');
        }
        
        print('📊 Total recipes: ${recipes.length}');
      } else {
        print('❌ Failed to fetch recipes: ${response.statusCode}');
        print('Response: ${response.body}');
      }
    } catch (e) {
      print('❌ Error checking database: $e');
    }
  }

  /// Check what's in storage
  static Future<void> checkStorage() async {
    try {
      print('🗂️  Checking storage bucket...');
      print('===============================');
      
      final url = Uri.parse('$supabaseUrl/storage/v1/object/list/recipe-images');
      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
        },
        body: '{"limit": 100, "prefix": ""}',
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> files = json.decode(response.body);
        
        print('📁 Files in recipe-images bucket:');
        for (final file in files) {
          final fileName = file['name'] as String;
          final size = file['metadata']?['size'] ?? 'Unknown size';
          final publicUrl = '$supabaseUrl/storage/v1/object/public/recipe-images/$fileName';
          
          print('📄 $fileName ($size bytes)');
          print('   🔗 $publicUrl');
          print('');
        }
        
        print('📊 Total files: ${files.length}');
      } else {
        print('❌ Failed to list storage files: ${response.statusCode}');
        print('Response: ${response.body}');
      }
    } catch (e) {
      print('❌ Error checking storage: $e');
    }
  }

  /// Test if a specific image URL works
  static Future<void> testImageUrl(String url) async {
    try {
      print('🧪 Testing image URL: $url');
      
      final response = await http.head(Uri.parse(url));
      
      if (response.statusCode == 200) {
        print('✅ Image accessible');
        print('   Content-Type: ${response.headers['content-type']}');
        print('   Content-Length: ${response.headers['content-length']} bytes');
      } else {
        print('❌ Image not accessible: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error testing image: $e');
    }
  }

  /// Update a specific recipe with test image
  static Future<void> updateRecipeImage(String recipeTitle, String imageUrl) async {
    try {
      print('🔄 Updating $recipeTitle with image: $imageUrl');
      
      final url = Uri.parse('$supabaseUrl/rest/v1/recipes?title=eq.$recipeTitle');
      final response = await http.patch(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
          'Prefer': 'return=minimal',
        },
        body: json.encode({
          'image_urls': [imageUrl]
        }),
      );
      
      if (response.statusCode == 204) {
        print('✅ Successfully updated $recipeTitle');
      } else {
        print('❌ Failed to update $recipeTitle: ${response.statusCode}');
        print('Response: ${response.body}');
      }
    } catch (e) {
      print('❌ Error updating recipe: $e');
    }
  }
}

void main(List<String> args) async {
  print('🔧 Recipe Image Debugger');
  print('========================\n');
  
  final String action = args.isNotEmpty ? args[0] : 'all';
  
  switch (action) {
    case 'db':
      await ImageDebugger.checkDatabase();
      break;
      
    case 'storage':
      await ImageDebugger.checkStorage();
      break;
      
    case 'test':
      if (args.length > 1) {
        await ImageDebugger.testImageUrl(args[1]);
      } else {
        print('Usage: dart scripts/debug_images.dart test <image_url>');
      }
      break;
      
    case 'update':
      if (args.length > 2) {
        await ImageDebugger.updateRecipeImage(args[1], args[2]);
      } else {
        print('Usage: dart scripts/debug_images.dart update <recipe_title> <image_url>');
      }
      break;
      
    case 'all':
    default:
      await ImageDebugger.checkDatabase();
      print('\n' + '='*50 + '\n');
      await ImageDebugger.checkStorage();
      break;
  }
  
  print('\n✨ Debug completed!');
}
