-- SQL script to set up the recipe-images storage bucket
-- Run this in Supabase SQL Editor

-- 1. Create the bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('recipe-images', 'recipe-images', true)
ON CONFLICT (id) DO UPDATE SET
  public = true;

-- 2. Set up RLS policies for the bucket
-- Allow public read access to all files
CREATE POLICY "Public read access for recipe images" ON storage.objects
FOR SELECT USING (bucket_id = 'recipe-images');

-- Allow authenticated users to upload files
CREATE POLICY "Authenticated users can upload recipe images" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'recipe-images' AND auth.role() = 'authenticated');

-- Allow authenticated users to update their own files
CREATE POLICY "Authenticated users can update recipe images" ON storage.objects
FOR UPDATE USING (bucket_id = 'recipe-images' AND auth.role() = 'authenticated');

-- Allow authenticated users to delete files
CREATE POLICY "Authenticated users can delete recipe images" ON storage.objects
FOR DELETE USING (bucket_id = 'recipe-images' AND auth.role() = 'authenticated');

-- 3. Verify the bucket was created
SELECT * FROM storage.buckets WHERE id = 'recipe-images';
