-- SQL script to fix RLS policies for recipe-images storage bucket
-- Run this in Supabase SQL Editor

-- 1. First, let's see current policies
SELECT * FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage';

-- 2. Drop existing policies that might be blocking (if any)
DROP POLICY IF EXISTS "Public read access for recipe images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload recipe images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update recipe images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete recipe images" ON storage.objects;

-- 3. Create new permissive policies for recipe-images bucket

-- Allow public read access to recipe images
CREATE POLICY "Public read recipe images" ON storage.objects
FOR SELECT USING (bucket_id = 'recipe-images');

-- Allow public upload to recipe images (for scripts and app)
CREATE POLICY "Public upload recipe images" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'recipe-images');

-- Allow public update of recipe images
CREATE POLICY "Public update recipe images" ON storage.objects
FOR UPDATE USING (bucket_id = 'recipe-images');

-- Allow public delete of recipe images (optional - you can remove this if you don't want public delete)
CREATE POLICY "Public delete recipe images" ON storage.objects
FOR DELETE USING (bucket_id = 'recipe-images');

-- 4. Make sure the bucket is public
UPDATE storage.buckets
SET public = true
WHERE id = 'recipe-images';

-- 5. Verify the setup
SELECT
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types
FROM storage.buckets
WHERE id = 'recipe-images';

-- 6. Check the new policies
SELECT
  policyname,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE tablename = 'objects'
AND schemaname = 'storage'
AND policyname LIKE '%recipe%';
