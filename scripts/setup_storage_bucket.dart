import 'dart:typed_data';
import 'package:http/http.dart' as http;

/// Script to set up the recipe-images storage bucket in Supabase
class StorageBucketSetup {
  static const String supabaseUrl = 'https://ovvathmpvaeeesmiywjb.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92dmF0aG1wdmFlZWVzbWl5d2piIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NDI3NjUsImV4cCI6MjA2NDQxODc2NX0.MYSVq47Zx0riqlptDGVTDP0QEX1lhecmdaQ56flZ-n4';

  /// Check if bucket exists
  static Future<bool> checkBucketExists() async {
    try {
      final url = Uri.parse('$supabaseUrl/storage/v1/bucket/recipe-images');
      final response = await http.get(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'apikey': supabaseAnonKey,
        },
      );
      
      print('Bucket check response: ${response.statusCode}');
      print('Response body: ${response.body}');
      
      return response.statusCode == 200;
    } catch (e) {
      print('Error checking bucket: $e');
      return false;
    }
  }

  /// Create the bucket
  static Future<bool> createBucket() async {
    try {
      final url = Uri.parse('$supabaseUrl/storage/v1/bucket');
      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'Content-Type': 'application/json',
          'apikey': supabaseAnonKey,
        },
        body: '''
        {
          "id": "recipe-images",
          "name": "recipe-images",
          "public": true,
          "file_size_limit": 52428800,
          "allowed_mime_types": ["image/jpeg", "image/png", "image/jpg"]
        }
        ''',
      );
      
      print('Create bucket response: ${response.statusCode}');
      print('Response body: ${response.body}');
      
      return response.statusCode == 200 || response.statusCode == 201;
    } catch (e) {
      print('Error creating bucket: $e');
      return false;
    }
  }

  /// List all buckets
  static Future<void> listBuckets() async {
    try {
      final url = Uri.parse('$supabaseUrl/storage/v1/bucket');
      final response = await http.get(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'apikey': supabaseAnonKey,
        },
      );
      
      print('List buckets response: ${response.statusCode}');
      print('Available buckets: ${response.body}');
    } catch (e) {
      print('Error listing buckets: $e');
    }
  }

  /// Test upload a small file
  static Future<bool> testUpload() async {
    try {
      // Create a small test image (1x1 pixel PNG)
      final testImageBytes = Uint8List.fromList([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
        0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
        0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44,
        0xAE, 0x42, 0x60, 0x82
      ]);

      final url = Uri.parse('$supabaseUrl/storage/v1/object/recipe-images/test.png');
      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer $supabaseAnonKey',
          'Content-Type': 'image/png',
          'apikey': supabaseAnonKey,
          'x-upsert': 'true',
        },
        body: testImageBytes,
      );
      
      print('Test upload response: ${response.statusCode}');
      print('Response body: ${response.body}');
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        print('✅ Test upload successful!');
        
        // Get the public URL
        final publicUrl = '$supabaseUrl/storage/v1/object/public/recipe-images/test.png';
        print('📷 Test image URL: $publicUrl');
        
        return true;
      } else {
        print('❌ Test upload failed');
        return false;
      }
    } catch (e) {
      print('Error testing upload: $e');
      return false;
    }
  }

  /// Complete setup process
  static Future<void> setupStorageBucket() async {
    print('🔧 Setting up recipe-images storage bucket...');
    print('=====================================');
    
    // Step 1: List existing buckets
    print('\n📋 Step 1: Listing existing buckets...');
    await listBuckets();
    
    // Step 2: Check if bucket exists
    print('\n🔍 Step 2: Checking if recipe-images bucket exists...');
    final bucketExists = await checkBucketExists();
    
    if (bucketExists) {
      print('✅ Bucket already exists!');
    } else {
      print('❌ Bucket does not exist. Creating...');
      
      // Step 3: Create bucket
      print('\n🏗️  Step 3: Creating recipe-images bucket...');
      final created = await createBucket();
      
      if (created) {
        print('✅ Bucket created successfully!');
      } else {
        print('❌ Failed to create bucket');
        print('💡 Please create it manually in Supabase dashboard:');
        print('   1. Go to Storage in Supabase dashboard');
        print('   2. Click "New bucket"');
        print('   3. Name: recipe-images');
        print('   4. Make it Public');
        return;
      }
    }
    
    // Step 4: Test upload
    print('\n🧪 Step 4: Testing upload functionality...');
    final uploadWorks = await testUpload();
    
    if (uploadWorks) {
      print('\n🎉 Setup completed successfully!');
      print('✅ Bucket exists and is accessible');
      print('✅ Upload functionality works');
      print('\n🚀 You can now run:');
      print('   dart scripts/upload_recipe_images.dart upload');
    } else {
      print('\n❌ Setup incomplete - upload test failed');
      print('💡 Please check bucket permissions in Supabase dashboard');
    }
  }
}

void main() async {
  await StorageBucketSetup.setupStorageBucket();
}
