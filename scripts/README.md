# Recipe Image Upload Script

This script downloads high-quality food images from Pexels and uploads them to your Supabase storage bucket, then updates the recipes database with the storage URLs.

## Prerequisites

1. **Supabase Setup**:
   - Ensure the `recipe-images` bucket exists in your Supabase storage
   - Bucket should be public or have proper RLS policies
   - Your Supabase credentials should be correct in the script

2. **Dependencies**:
   - The `http` package is already added to your project
   - No additional dependencies needed

## Usage

### Option 1: Download and Upload to Storage (Recommended)

This downloads images from Pexels and uploads them to your Supabase storage:

```bash
dart scripts/upload_recipe_images.dart upload
```

or simply:

```bash
dart scripts/upload_recipe_images.dart
```

**What it does**:
- Downloads high-quality food images from Pexels
- Uploads them to your `recipe-images` Supabase storage bucket
- Updates the recipes database with the storage URLs
- Provides detailed progress logging

### Option 2: Use Direct Pexels URLs

This updates the database directly with Pexels URLs (no storage upload):

```bash
dart scripts/upload_recipe_images.dart direct
```

**What it does**:
- Updates recipes database with direct Pexels image URLs
- Faster but relies on external URLs
- Good for testing or if storage upload fails

### Option 3: Check Storage Bucket

Check if your storage bucket is accessible:

```bash
dart scripts/upload_recipe_images.dart check
```

### Option 4: List Storage Files

List all files currently in your storage bucket:

```bash
dart scripts/upload_recipe_images.dart list
```

## Expected Output

### Successful Upload Process:
```
🍽️  Recipe Image Uploader
========================
✅ Storage bucket "recipe-images" is accessible

🚀 Starting download and upload to storage...
📊 Total recipes to process: 14

📝 Processing Kerala Fish Curry (1/14)...
  📥 Downloading image 1/1 from Pexels...
  📤 Uploading kerala-fish-curry-1.jpg to Supabase storage...
  ✅ Successfully uploaded: kerala-fish-curry-1.jpg
  🎉 Completed Kerala Fish Curry: 1 images uploaded

[... continues for all recipes ...]

🔄 Updating database with storage URLs...
✅ Updated database for Kerala Fish Curry with 1 storage URLs
[... continues for all recipes ...]

🎊 Process completed!
📈 Summary:
   - Total recipes processed: 14
   - Recipes with uploaded images: 14
   - Total images uploaded: 14

✨ Script completed!
```

## Troubleshooting

### Storage Bucket Not Accessible
```
❌ Cannot proceed - storage bucket not accessible
Please check:
1. Bucket "recipe-images" exists in Supabase dashboard
2. Bucket has proper permissions/RLS policies
3. Your Supabase credentials are correct
```

**Solutions**:
1. Go to Supabase Dashboard → Storage
2. Create `recipe-images` bucket if it doesn't exist
3. Make bucket public or set up proper RLS policies
4. Verify your Supabase URL and anon key in the script

### Image Download Failures
```
❌ Error processing image 1 for Kerala Fish Curry: Failed to download image: 404
```

**Solutions**:
- Check your internet connection
- Pexels URLs might have changed - update with new URLs
- Try the `direct` option as fallback

### Database Update Failures
```
❌ Error updating database for Kerala Fish Curry: Failed to update recipe: 404
```

**Solutions**:
- Verify recipe names match exactly in your database
- Check if recipes table exists and has `image_urls` column
- Verify database permissions

## File Structure After Upload

Your Supabase storage will contain files like:
```
recipe-images/
├── kerala-fish-curry-1.jpg
├── chicken-biriyani-1.jpg
├── masala-dosa-1.jpg
├── butter-chicken-1.jpg
├── mango-lassi-1.jpg
├── chocolate-chip-cookies-1.jpg
├── appam-with-stew-1.jpg
├── puttu-and-kadala-curry-1.jpg
├── sambar-rice-1.jpg
├── avocado-toast-1.jpg
├── payasam-1.jpg
├── quinoa-salad-bowl-1.jpg
├── masala-chai-1.jpg
└── fish-molee-1.jpg
```

## Database Updates

The script updates the `recipes` table `image_urls` column with storage URLs like:
```sql
UPDATE recipes 
SET image_urls = ARRAY['https://ovvathmpvaeeesmiywjb.supabase.co/storage/v1/object/public/recipe-images/kerala-fish-curry-1.jpg']
WHERE title = 'Kerala Fish Curry';
```

## Notes

- Images are high-quality (800x600) from Pexels
- Rate limiting is implemented (500ms delay between uploads)
- Duplicate uploads are handled with upsert
- All images are in JPG format for consistency
- Filenames are generated from recipe names (lowercase, no spaces)

## Next Steps

After running the script:
1. Check your Supabase storage dashboard to verify uploads
2. Test the app to see images displaying correctly
3. Use the app's upload images screen for additional testing
4. Create new recipes using the recipe creation screen
